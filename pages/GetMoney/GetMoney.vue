<template>
	<view class="page">
		<!-- 选择反馈类型 -->
		<view class="contact-way" v-show="amount!='' && amount!='可用金额：undefined'">
			<input type="text" v-model="amount" placeholder="可用金额" disabled="false">
		</view>
		<view class="feedback-type">
			<view class="title" @click="choose">
				<text>提现方式：{{TypeArray[TypeIndex]}}</text>
			</view>
			<view class="picker">
				<picker @change="FeedbackTypeCh" :value="TypeIndex" :range="TypeArray" style="height: 100%;">
						<view class="uni-input">{{TypeArray[TypeIndex]}}</view>
				</picker>
			</view>
			<view class="more"  @click="choose">
				<text class="iconfont icon-more1"></text>
			</view>
		</view>
		<view class="contact-way">
			<input type="number" v-model="number" placeholder="请输入金额 需大于1万">
		</view>
		<!-- 反馈内容 -->
		<view class="feedback-data" style="height: 360rpx;">
			<view class="content">
				<textarea v-model="content" value="" placeholder="微信添加方式/支付宝账号/银行账户信息" />
			</view>
		</view>
		<!-- 联系方式 -->
		<view class="contact-way">
			<input type="text" v-model="mobile" placeholder="联系手机号">
		</view>
		<!-- 提交 -->
		<view class="submit-btn" @click="postFeedBack">
			<text>提交</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				TypeArray: ['微信', '支付宝', '银行卡'],
				TypeIndex: 0,
				content:'',
				mobile:'',
				amount:'',
				number:''
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login:true})
			this.amount = "可用金额："+params.amount
		},
		methods:{
			choose(){
				this.$refs['file-picker']?.choose()
			},
			postFeedBack(){
				var that = this
				uni.showModal({
					title:'是否确定提交提现',
					success:function(re){
						if(re.confirm){
							that.$http.post('postGetMoney', {
								token: getApp().globalData.token,
								content:that.content,
								number:that.number,
								mobile:that.mobile,
								ctype:that.TypeArray[that.TypeIndex]
							}).then(res => {
								uni.stopPullDownRefresh();
								if (res.code == 0) {
									uni.showToast({
										title:"等待审核提现"
									})
									setTimeout(function(){
										uni.navigateBack()
									},1500)
								} else {
									uni.showToast({
										title:res.msg
									})
								}
							})
						}
					}
				})
			},
			/**
			 * 反馈类型
			 * @param {Object} val
			 */
			FeedbackTypeCh(val){
				
				this.TypeIndex = val.detail.value;
				
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'GetMoney.scss';
</style>
