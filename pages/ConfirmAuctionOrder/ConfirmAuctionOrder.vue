<template>
	<view class="page">
		<!-- 地址 -->
		<view class="address-data" v-show="TypeIndex==0">
			<view class="address-list" @click="onSkip('address')">
				<block v-if="address!=null || showAdd">
					<view class="list">
						<text>地址：{{address.address1}}</text>
					</view>
					<view class="list">
						<text class="address">详细地址：{{address.address2}}</text>
					</view>
					<view class="list">
						<text>收件人：{{address.username}}</text>
						<text>{{address.mobile}}</text>
					</view>
				</block>
				
				<view class="list">
					<text class="tips">(点击添加或修改收件地址)</text>
				</view>
			</view>
			<view class="bar">

			</view>
		</view>
		<!-- 商品 -->
		<view class="goods-data">
			<view class="goods-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="list">
					<view class="thumb">
						<image :src="api + readOrderArr.goods.img" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="name one-omit">{{readOrderArr.goods.goodsTitle}}</text>
							<text class="attr">{{number}}件</text>
						</view>
						<view class="price-number">
							<view class="price" v-if="fromType=='auction'">
								<text class="min">提货价：￥</text>
								<text class="max">{{readOrderArr.goods.price2}}</text>
								<!-- <text class="min">.00</text> -->
							</view>
							<view class="price" v-else>
								<text class="min">市场价：￥</text>
								<text class="max">{{readOrderArr.goods.price2}}</text>
								<!-- <text class="min">.00</text> -->
							</view>
							<view class="number">
								<text>x {{number}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="delivery">
				<div class="list">
					<view class="title">配送</view>
					<view class="content">
						<text>
							<picker @change="FeedbackTypeCh" :value="TypeIndex" :range="TypeArray" style="height: 100%;">
									<view class="uni-input">{{TypeArray[TypeIndex]}}</view>
							</picker>
						</text>
						<text class="iconfont icon-more"></text>
					</view>
				</div>
				<div class="list">
					<view class="title">备注</view>
					<view class="content">
						<input type="text" v-model="readOrderArr.content" placeholder="选填,建议先和商家沟通确认">
					</view>
				</div>
			</view>
		</view>
		
		<!-- 订单金额 -->
		<view class="order-price">
			<view class="price-list">
				<view class="list">
					<view class="title">
						<text>下单数量</text>
					</view>
					<view class="price">
						<text style="border: 1px solid #ccc;margin-right: 20rpx;padding: 4rpx 8rpx;" class="" @click="jiajia(-100000)">最小</text>
						<text  style="border: 1px solid #ccc;margin-right: 0rpx;padding: 4rpx 8rpx;" class="iconfont icon-jian" @click="jiajia(-1)"></text>
						<input type="tel" v-model="number" maxlength="8" style="width: 100rpx;margin: 0 10rpx;text-align: center;">
						<text style="border: 1px solid #ccc;margin-left: 0rpx;padding: 4rpx 8rpx;" class="iconfont icon-jia" @click="jiajia(1)"></text>
						<text style="border: 1px solid #ccc;margin-left: 20rpx;padding: 4rpx 8rpx;" @click="jiajia(100000)">最大</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>商品金额</text>
					</view>
					<view class="price">
						<text>￥{{readOrderArr.goods.price2 * number}}</text>
					</view>
				</view>
				
				<view class="list">
					<view class="title">
						<text>运费</text>
					</view>
					<view class="price">
						<text class="highlight">￥0.00</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 订单金额 -->
		<view class="pay-way" style="margin-top: 20rpx;">
			<block v-if="fromType=='goods' && (readOrderArr.goods.id == 29 || readOrderArr.goods.id == 30)">
				<view class="pay-list">
					<view class="list" @click="onPayWay()">
						<view class="pay-type">
							<text class="iconfont icon-qianbao"></text>
							<text>参拍货款({{readOrderArr.amountPHK}})</text>
						</view>
						<view class="check">
							<text class="iconfont icon-checked action"></text>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="pay-list" v-if="fromType=='auction'">
					<view class="list" @click="onPayWay()">
						
						<view class="pay-type">
							<text class="iconfont icon-qianbao"></text>
							<text>提货余额({{readOrderArr.goods.income}})</text>
						</view>
						<view class="check">
							<text class="iconfont icon-checked action"></text>
						</view>
					</view>
				</view>
				<view class="pay-list" v-if="fromType=='goods'">
					<view class="list" @click="setPayIndex(1)" v-if="readOrderArr.goods.cid != 229">
						
						<view class="pay-type">
							<text class="iconfont icon-qianbao"></text>
							<text>结拍货款({{readOrderArr.goods.income}})</text>
						</view>
						<view class="check">
							<text class="iconfont icon-checked" :class="payIndex==1?'action':''"></text>
						</view>
					</view>
					<view class="list" @click="setPayIndex(2)">
						
						<view class="pay-type">
							<text class="iconfont icon-qianbao"></text>
							<text>助力金({{readOrderArr.amountZL}})</text>
						</view>
						<view class="check">
							<text class="iconfont icon-checked" :class="payIndex==2?'action':''"></text>
						</view>
					</view>
				</view>
			</block>
			
		</view>
		<!-- 地址提示 -->
		<view class="address-tips" :style="scrollTop >= 100 ? '':'display:none'">
			<text v-if="readOrderArr.address!=null">{{readOrderArr.address.address1}}{{readOrderArr.address.address2}}</text>
		</view>
		<!-- 底部合计提交 -->
		<view class="footer-submit">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">{{readOrderArr.goods.price2 * number}}</text>
				<!-- <text class="min">.00</text> -->
			</view>
			<view class="submit" @click="onSubmit">
				<text>提交订单</text>
			</view>
		</view>
		<!-- 发票 -->
		<invoice-info ref="InvoiceInfo"></invoice-info>
		<!-- 优惠券 -->
		<use-coupon ref="UseCoupon"></use-coupon>
	</view>
</template>

<script>
	import InvoiceInfo from '../../components/InvoiceInfo/InvoiceInfo.vue';
	import UseCoupon from '../../components/UseCoupon/UseCoupon.vue'
	export default {
		components:{
			// 发票
			InvoiceInfo,
			// 优惠券
			UseCoupon,
		},
		data() {
			return {
				TypeArray:["快递运输"],
				TypeIndex:0,
				scrollTop: 0,
				api:getApp().globalData.apiUrl,
				id:0,
				number:0,
				fromType:'auction',
				readOrderArr:{address:{address1:''},goods:{img:''}},
				showAdd:false,
				payIndex:0,
				address:null
			};
		},
		onPageScroll(e){
			this.scrollTop = e.scrollTop;
		},
		onLoad(paprams) {
			this.$login.checkLogin({login:true})
			this.id=paprams.id
			this.number=1
			this.fromType=paprams.fromType
			if(this.id<1 || this.number<1){
				uni.showToast({
					title:"表单错误"
				})
			}else{
				this.readyOrder(this.fromType)
			}
			if(this.fromType=='goods'){
				this.payIndex=1
			}
		},
		onShow() {
			this.getAddress()
		},
		methods:{
			setPayIndex($cint){
				this.payIndex=$cint
				this.calcCanBuy()
			},
			jiajia($cint){
				if($cint == -100000){
					this.number=1
					return
				}
				if($cint == 100000){
					this.calcCanBuy()
					return
				}
				this.number = this.number*1 + $cint*1
				if(this.number<1){
					this.number=1
				}
			},
			calcCanBuy(){
				if(this.fromType == 'auction'){
					this.number = Math.floor(this.readOrderArr.goods.income / this.readOrderArr.goods.price2)
				}
				if(this.fromType == 'goods'){
					if(this.payIndex==1){
						this.number = Math.floor(this.readOrderArr.goods.income / this.readOrderArr.goods.price2)
					}
					if(this.payIndex==2){
						this.number = Math.floor(this.readOrderArr.amountZL / this.readOrderArr.goods.price2)
					}
				}
			},
			getAddress(){
				var temAddress = getApp().globalData.selectAddress
				if(Object.keys(temAddress).length > 0 ){
					this.showAdd = true
					this.readOrderArr.address = temAddress
					this.address = temAddress
					// getApp().globalData.selectAddress={}
				}
			},
			FeedbackTypeCh(val){
				
				this.TypeIndex = val.detail.value;
				
			},
			readyOrder($from='auction'){
				uni.showLoading({
					title:"加载中..."
				})
				var gotoUrl="";
				if($from == 'auction'){
					gotoUrl = "readAuctionOrder"
				}
				if($from == 'goods'){
					gotoUrl = "readGoodsOrder"
				}
				this.$http.get(gotoUrl, {
					token: getApp().globalData.token,
					id:this.id,
					number:this.number
				}).then(res => {
					uni.hideLoading()
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.readOrderArr = res.data
						if(this.readOrderArr.goods.cid == 229){
							this.payIndex = 2
						}
						
						this.getAddress()
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 提交订单
			 */
			onSubmit(){
				var that=this
				if(this.readOrderArr.address==null && this.TypeIndex==0){
					uni.showToast({
						title:"请添加收件地址"
					})
					return false;
				}
				uni.showModal({
					title:"是否确认下单？",
					success:function(re){
						if(re.confirm){
							that.$http.post('saveOrder', {
								token: getApp().globalData.token,
								id:that.id,
								number:that.number,
								data:JSON.stringify(that.readOrderArr),
								TypeIndex:that.TypeIndex,
								fromType:that.fromType,
								payIndex:that.payIndex
							}).then(res => {
								uni.showToast({
									title:res.msg
								})
								if (res.code == 0) {
									setTimeout(function(){
										uni.redirectTo({
											url: '/pages/MyOrderList/MyOrderList?type=2',
										})
									},1000)
								}
							})
						}
					}
				})
				
				
			},
      /**
       * 跳转点击
       * @param {String} type 跳转类型
       */
      onSkip(type){
        switch (type){
          case 'address':
            uni.navigateTo({
              url: '/pages/AddressList/AddressList',
            })
            break;
        }
      }
		}
	}
</script>

<style scoped lang="scss">
	@import '../CashierDesk/CashierDesk.scss';
	@import 'ConfirmAuctionOrder.scss';
</style>
