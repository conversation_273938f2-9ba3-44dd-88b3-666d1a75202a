<template>
	<view class="page">
		<!-- 订单tab -->
		<!-- 订单列表 -->
		<view class="order-list">
			<view v-if="ordersList.length<1" class="on-anything">
				———— 没有任何数据 ————
			</view>
			<view class="list" v-for="(item,index) in ordersList" :key="index">
				<view class="title-status">
					<view class="title">
						<text>下单时间：{{item.addTime}}</text>
					</view>
				</view>
				<view class="goods-list">
					<view class="goods">
						<view class="thumb">
							<image :src="api + item.img" mode=""></image>
						</view>
						<view class="item">
							<view class="goods-name">
								<text class="two-omit">{{item.goodsTitle}}</text>
								<text class="two-omit">数量：{{item.numbers}}</text>
								<text class="two-omit">总金额：{{item.amount}}</text>
								<text class="two-omit">提货价：{{item.price2}}</text>
							</view>
							<view class="goods-price">
								<text class="min">￥</text>
								<text class="max">{{item.income}}</text>
								<!-- <text class="min">.00</text> -->
							</view>
						</view>
					</view>
				</view>
				<view class="status-btn">
					<block v-if="item.todaySell<1">
						<view class="btn" @click.stop="gotoUrl(1,item.goodsID,item.roundID)">
							<text>去委拍</text>
						</view>
					</block>
					<block v-else>
						<block v-if="item.endDate>item.nowDate && item.bStatus>0">
							<view class="btn" @click.stop="gotoUrl(2,item.id)">
								<text>去提货</text>
							</view>
						</block>
						<block v-else>
							收益转：
							<view class="btn" @click="amountTo('HK',item.id)">
								<text>结拍货款</text>
							</view>
							<view class="btn" @click="amountTo('ZL',item.id)">
								<text>助力金</text>
							</view>
						</block>
					</block>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				OrderType: 0,
				ordersList:{},
				api:getApp().globalData.apiUrl
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login:true})
			this.OrderType = params.type;
			this.getOrderList()
		},
		onShow() {
			if(this.OrderType==4){
				// this.onOrderTab(0)
			}
		},
		methods:{
			amountTo($str='',$id=0){
				if($str=='HK'){
					uni.showModal({
						title:"请确认操作",
						content:"转到结拍货款",
						success:res=>{
							if(res.confirm){
								this.amountToPost("HK",$id)
							}
						}
					})
				}
				if($str=='ZL'){
					uni.showModal({
						title:"请确认操作",
						content:"转到助力金",
						success:res=>{
							if(res.confirm){
								this.amountToPost("ZL",$id)
							}
						}
					})
				}
			},
			amountToPost($amount='',$id=0){
				var that = this
				that.$http.get('amountToPost', {
					token: getApp().globalData.token,
					goto:$amount,
					id:$id
				}).then(res => {
					uni.showToast({
						title:res.msg
					})
				})
			},
			gotoUrl($type=0,$cint=0,$cint2=0){
				if($type == 1) uni.navigateTo({url:'/pages/GoodsDetails/GoodsDetails?id='+$cint+"&round="+$cint2})
				if($type == 2) uni.navigateTo({url:'/pages/ConfirmAuctionOrder/ConfirmAuctionOrder?id='+$cint+'&fromType=auction'})
			},
			delOrders($cid){
				var that = this
				uni.showModal({
					title:"是否确定删除?",
					success:function(res){
						if(res.confirm){
							that.$http.get('orderDel', {
								token: getApp().globalData.token,
								orderID:$cid
							}).then(res => {
								uni.showToast({
									title:res.msg
								})
								if (res.code == 0) {
									setTimeout(function(){
										that.getOrderList()
									},1500)
								}
							})
						}
					}
				})
			},
			pay(orderID){
				uni.redirectTo({
					url: '/pages/CashierDesk/CashierDesk?orderID='+orderID,
				})
			},
			getOrderList(){
				var that = this
				uni.showLoading({
					title:"加载中..."
				})
				this.$http.get('getAuctionList', { 
					token: getApp().globalData.token,
					OrderType:that.OrderType
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						that.ordersList = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 返回点击
			 */
			onBack(){
				uni.navigateBack();
			},
			/**
			 * 订单tab点击
			 */
			onOrderTab(type){
				this.OrderType = type;
				// #ifdef H5
				if(type==4 && 1==2){
					uni.navigateTo({
						url: '/pages/AfterSalesOrder/AfterSalesOrder',
					})
				}else{
					uni.redirectTo({
						url: '/pages/MyOrderList/MyOrderList?type=' + type,
					})
				}
				
				//#endif
			},
			/**
			 * 订单列表点击
			 */
			onOrderList(ordersID){
				uni.navigateTo({
					url: '/pages/OrderDetails/OrderDetails?ordersID='+ordersID,
				})
			},
      /**
       * 评价点击
       */
      onEvaluate(){
		uni.navigateTo({
			url: '/pages/MyEvaluatePush/MyEvaluatePush'
        })
      }
		}
	}
</script>

<style scoped lang="scss">
	@import 'MyAuctionList.scss';
</style>
