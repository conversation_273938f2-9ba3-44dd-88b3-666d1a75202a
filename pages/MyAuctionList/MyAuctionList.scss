.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}
/* 顶部返回 */
.head-back{
	position: fixed;
	left: 0;
	top: 0;
	z-index: 10;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;
	/* #ifdef APP-PLUS */
	height: calc(50rpx + var(--status-bar-height));
	padding-top: var(--status-bar-height);
	/* #endif */
	/* #ifdef MP */
	height: 150rpx;
	padding-top: 20rpx;
	/* #endif */
	.back{
		position: absolute;
		left: 0;
		top: 0;
		/* #ifdef APP-PLUS */
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		padding-top: 20rpx;
		/* #endif */
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		height: 100%;
		text{
			width: 20rpx;
			height: 20rpx;
			border-left: 2rpx solid #555555;
			border-bottom: 2rpx solid #555555;
			transform: rotate(45deg);
		}
	}
	.title{
		display: flex;
		align-items: center;
		text{
			font-size: 28rpx;
			color: #222222;
		}
	}
	.more-icon{
		position: absolute;
		right: 0;
		top: 0;
		/* #ifdef APP-PLUS */
		right: 0rpx;
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		right: 220rpx;
		padding-top: 20rpx;
		/* #endif */
		display: flex;
		align-items: center;
		height: 100%;
		.icon-list{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80rpx;
			height: 100%;
			text{
				font-size: 34rpx;
				color: #222222;
			}
		}
	}
}

/* 订单tab */
.order-tab{
	position: fixed;
	left: 0;
	top: 100rpx;
	/* #ifdef APP-PLUS */
	top: calc(50rpx + var(--status-bar-height));
	/* #endif */
	z-index: 10;
	display: flex;
	align-items: center;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;
	.tab{
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 20%;
		height: 80%;
		text{
			font-size: 26rpx;
			color: #959595;
		}
	}
	.action{
		text{
			color: #222222;
		}
		.line{
			position: absolute;
			left: 50%;
			bottom: 0;
			width: 60rpx;
			height: 6rpx;
			background: linear-gradient(to right,$base,#f6f6f6);
			transform: translate(-50%,0);
		}
	}
}

/* 订单列表 */
.order-list{
	width: 100%;
	margin-top: 0rpx;
	/* #ifdef APP-PLUS */
	margin-top: calc(0rpx + var(--status-bar-height));
	/* #endif */
	.list{
		padding: 0 4%;
		min-height: 400rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		.title-status{
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;
			.title{
				display: flex;
				align-items: center;
				text{
					font-size: 26rpx;
					font-weight: bold;
					color: #222222;
				}
			}
			.status{
				display: flex;
				align-items: center;
				text{
					font-size: 26rpx;
					color: $base;
				}
				.del{
					padding: 10rpx;
					font-size: 34rpx;
					color: #222222;
					background-color: #f6f6f6;
					border-radius: 100%;
					margin-left: 20rpx;
				}
			}
		}
		.goods-list{
			width: 100%;
			.goods{
				display: flex;
				align-items: center;
				width: 100%;
				height: 200rpx;
				.thumb{
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;
					image{
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}
				.item{
					display: flex;
					align-items: center;
					width: 70%;
					height: 100%;
					.goods-name{
						width: 70%;
						text{
							font-size: 26rpx;
							color: #555555;
						}
					}
					.goods-price{
						display: flex;
						align-items: center;
						justify-content: flex-end;
						width: 30%;
						text{
							color: #222222;
						}
						.min{
							font-size: 26rpx;
						}
						.max{
							font-size: 34rpx;
						}
					}
				}
			}
		}
		.status-btn{
			display: flex;
			align-items: center;
			justify-content: flex-end;
			width: 100%;
			height: 100rpx;
			.btn{
				padding: 10rpx 30rpx;
				border: 2rpx solid #EEEEEE;
				border-radius: 100rpx;
				margin-left: 20rpx;
				text{
					font-size: 26rpx;
					color: #555555;
				}
			}
			.action{
				border: 2rpx solid $base;
				text{
					color: $base;
				}
			}
		}
	}
}