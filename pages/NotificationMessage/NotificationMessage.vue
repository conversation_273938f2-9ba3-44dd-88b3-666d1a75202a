<template>
	<view class="page">
		<view class="inform-list">
			<view class="list" v-for="(item,index) in list" :key="index" @click="go(item.id)">
				<view class="date">
					<text>{{item.addTime}}</text>
				</view>
				<view class="item">
					<view class="title">
						<text class="one-omit">{{item.title}}</text>
					</view>
					<view class="describe">
						<text class="two-omit">{{item.title2}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				token:getApp().globalData.token,
				list:[],
				page:1,
				status:''
			};
		},
		onLoad() {
			this.getList()
		},
		onShow() {
			this.$login.checkLogin({login:true})
		},
		onReachBottom() {
			this.page++
			this.getList()
		},
		methods:{
			go($cid){
				uni.navigateTo({
					url:"/pages/ArticleDetails/ArticleDetails?id="+$cid
				})
			},
			getList(){
				uni.showLoading({
					title:"加载中..."
				})
				var that = this
				this.$http.get('getNoticList', {
					page: that.page,
					limit: 10,
					token: that.token
				}).then(res => {
					uni.hideLoading()
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						if (res.data.length == 0) {
							this.status = 'noMore'
						} else {
							this.list = this.list.concat(res.data);
							if (res.data.length < 10) {
								this.status = 'noMore'
							}
						}
					} else {
						this.status = 'noMore'
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'NotificationMessage.scss';
</style>
