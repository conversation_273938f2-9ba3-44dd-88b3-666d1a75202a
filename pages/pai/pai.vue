<template>
	<view class="page">
		<view class="head-back">
			<view class="title s-title-w">
				全部拍品
			</view>
		</view>
		<!-- 分类列表 -->
		<view class="classify-list s-flex s-flex-bt">
			<view class="list" @click="select(index)" v-for="(item,index) in rounds" :key="index"
				:style="index == selectID?'border-bottom: red solid 2px;':''">
				<view class="s-flex" style="align-items: center;">
					<view class="thumb">
						<image :src="api+item.file"></image>
					</view>
					<view class="name"><text class="one-omit">{{item.title}}</text></view>
				</view>
			</view>
		</view>
		<!-- 更多热卖 -->
		<view class="more-hot">
			<view class="hot-title">
				<view class="title">
					<text class="iconfont icon-xiedian"></text>
					<text class="icon" style="font-weight: bold;">拍卖进行中</text>
					<text class="iconfont icon-xiedian"></text>
				</view>
			</view>
			<view class="goods-list">
				<view class="list" v-for="(item,index) in goodsList" @click="onSkip('goods',item.id)" :key="index">
					<view class="pictrue s-flex-column s-pos-rel">
						<image :src="item.img" mode="aspectFill" style="width: 100%;"></image>
						<view class="s-paiBG s-pos-abs s-flex s-flex-bt">
							<view>
								<view>委拍时间</view>
								<view>{{item.sellTime}}</view>
							</view>
							<view class="s-pos-rel">
								<view>参拍时间</view>
								<view>{{item.auctionTime}}</view>
								<view class="s-sort-boder"></view>
							</view>
						</view>
					</view>
					<view class="title-tag">
						<view class="tag">
							<!-- <text v-if="item.is_goods === 1">下单</text> -->
							{{item.name}}
						</view>
					</view>
					<view class="price-info">
						<view class="user-price">
							<text class="min">￥</text>
							<text class="max">{{item.vip_price}}</text>
						</view>
						<view class="vip-price">
							<!-- <image src="/static/vip_ico.png"></image> -->
							<view class="s-btn-s">去参拍</view>
						</view>
					</view>
				</view>
				<view class="on-anything">
					———— 没有更多了 ————
				</view>
			</view>
		</view>
		<!-- tabbar -->
		<TabBar :tabBarShow="2"></TabBar>
	</view>
</template>

<script>
	import TabBar from '../../components/TabBar/TabBar.vue';
	export default {
		components: {
			TabBar
		},
		data() {
			return {
				api: getApp().globalData.apiUrl,
				rounds: [],
				selectID: 0,
				goodsList: [],
				page: 1,
				goodsList: [],
				classGoodsList: [],
				round: 0
			};
		},
		onLoad() {
			this.getRound()
			// this.getGoods()
		},
		onReachBottom() {
			this.page++
			this.getGoods()
		},
		methods: {
			getGoods() {
				uni.showLoading({
					title: "加载中..."
				})
				var that = this
				this.$http.get('getGoods', {
					page: that.page,
					limit: 10,
					token: getApp().globalData.token,
					round: that.round
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data.length == 0) {
							this.status = 'noMore'
						} else {
							this.goodsList = this.goodsList.concat(res.data);
							if (res.data.length < 10) {
								this.status = 'noMore'
							}
						}
					} else {
						this.status = 'noMore'
					}
				})
			},
			select(cint) {
				this.selectID = cint
				this.page = 1
				this.goodsList = []
				this.round = this.rounds[cint].id
				this.getGoods()
			},
			getRound() {
				var that = this
				this.$http.get('getRound', {

				}).then(res => {
					if (res.code == 0) {
						that.rounds = res.data
						that.round = res.data[0].id
						that.getGoods()
					}
				})
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type, id) {
				switch (type) {
					case 'classify':
						uni.navigateTo({
							url: '/pages/SearchGoodsList/SearchGoodsList',
						})
						break;
					case 'goods':
						uni.navigateTo({
							url: '/pages/GoodsDetails/GoodsDetails?id=' + id + "&round=" + this.round,
							animationType: 'zoom-fade-out',
							animationDuration: 200
						})
						break;
				}
			}
		}
	};
</script>

<style lang="scss">
	@import 'pai.scss';
</style>