<template>
	<view class="page">
		<view class="message-list">
			<view class="list" @click="onMessage('activity')">
				<view class="icon-data">
					<view class="icon">
						<image src="/static/hd-mess.png" mode=""></image>
					</view>
					<view class="data">
						<view class="title">
							<text>行业文章</text>
						</view>
						<view class="describe">
							<text>行业热门文章 政策文件</text>
						</view>
					</view>
				</view>
				<view class="more">
					{{articleNum>0?articleNum:''}}
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<view class="list" @click="onMessage('inform')">
				<view class="icon-data">
					<view class="icon" style="background-color: rgba(255, 125, 88, 1);">
						<image src="/static/tz-mess.png" mode=""></image>
					</view>
					<view class="data">
						<view class="title">
							<text>通知消息</text>
						</view>
						<view class="describe">
							<text>重点消息 规则和技巧</text>
						</view>
					</view>
				</view>
				<view class="more">
					{{messageNum>0?messageNum:''}}
					<text class="iconfont icon-more"></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				articleNum:0,
				messageNum:0,
				token:getApp().globalData.token
			};
		},
		onLoad() {
			this.$login.checkLogin({login:true})
			this.getNumber()
		},
		methods:{
			getNumber(){
				//获取消息和文章数量
				this.$http.get('getMessageNumber', {
					token: this.token
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.articleNum = res.data[0]
						this.messageNum = res.data[1]
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 消息点击
			 * @param {String} type
			 */
			onMessage(type){
				switch(type){
					case 'activity':
						uni.navigateTo({
							url: '/pages/DiscountsActivity/DiscountsActivity',
						})
						break;
					case 'inform':
						uni.navigateTo({
							url: '/pages/NotificationMessage/NotificationMessage',
						})
						break;
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'Message.scss';
</style>
