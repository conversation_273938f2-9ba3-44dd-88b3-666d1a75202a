<template>
	<view class="page">
		<view class="my-top">
			<!-- head -->
			<view class="head" :style="'background-color: rgba(255,255,255,'+(scrollTop/50)+');'">
				<view class="portrait">
					<image v-if="userInfo.headImg=='' || userInfo.headImg==null" v-show="scrollTop>20"
						src="/static/head.png"></image>
					<image v-else v-show="scrollTop>20" :src="api+userInfo.headImg"></image>
				</view>
				<view class="title">
					<text v-show="scrollTop>20">我的</text>
				</view>
				<view class="setting-mess">
					<view class="setting" @click="onSetting">
						<text class="iconfont icon-setting" :style="scrollTop>20?'color:#333333':''"></text>
					</view>
					<view class="mess" @click="onMessage">
						<text class="iconfont icon-xiaoxi" :style="scrollTop>20?'color:#333333':''"></text>
					</view>
				</view>
			</view>
			<!-- 用户信息 -->
			<view class="user-info" style="display: none">
				<view class="portrait">
					<image v-if="userInfo.headImg=='' || userInfo.headImg==null" src="/static/head.png"></image>
					<image v-else :src="userInfo.headImg"></image>
				</view>
				<view class="info">
					<view class="nickname">
						<text>{{userInfo.nickName}}</text>
					</view>
					<view class="rank">
						<image src="/static/rank.png"></image>
						<text>v1</text>
					</view>
				</view>
			</view>
			<view class="user-info">
				<view class="portrait" @click="headImg">
					<image v-if="userInfo.headImg=='' || userInfo.headImg==null" src="/static/head.png"></image>
					<image v-else :src="api+userInfo.headImg"></image>
				</view>
				<view class="info" style="position: relative;">
					<view class="nickname" @click="onSetting">
						<text>{{userInfo.nickName}}</text>
					</view>
					<view class="rank" style="color: #fff;">
						{{userInfo.mobile}}
							<image src="/static/rank.png"></image>
							<view style="display: inline-block;" @click="showLevelTime">{{userInfo.levelStr}}</view>
							<view style="display: inline-block;" @click="showAgentTime" v-if="userInfo.agent">
							<text class="iconfont icon-vip" style="font-size: 50%;color: yellow;"></text>
							{{userInfo.agent?'代理：'+userInfo.agent:''}}</view>
					</view>
					<view class="tips" :style="!levelTimeShow?'display: none;':''">等级期限：{{userInfo.levelStr=='用户'?'无期限':userInfo.levelTime}}</view>
					<view class="tips" :style="!agentTimeShow?'display: none;':''">代理期限：{{userInfo.agentTime}}</view>
					<!--          <view class="nickname">
			<text class="iconfont icon-vip">{{userInfo.levelStr}}</text>
          </view> -->
				</view>
			</view>
			<!-- 关注区 -->
			<!-- 			<view class="focus-area">
				<view class="list" @click="onCollect('goods')">
					<view class="num">
						<text>28</text>
					</view>
					<view class="title">
						<text>商品关注</text>
					</view>
				</view>
				<view class="list" @click="onCollect('content')">
					<view class="num">
						<text>28</text>
					</view>
					<view class="title">
						<text>喜欢的内容</text>
					</view>
				</view>
				<view class="list" @click="onCollect('record')">
					<view class="num">
						<text>28</text>
					</view>
					<view class="title">
						<text>浏览记录</text>
					</view>
				</view>
			</view> -->
			<!-- 会员 -->
			<view class="vip-info" @click="onMmeberVip">
				<view class="vip">
					<text>推广计划</text>
					<text class="line"></text>
				</view>
				<view class="vip-explain">
					<text>介绍周围朋友一起参与轻松购物</text>
				</view>
				<view class="vip-btn">
					<text>立即行动</text>
				</view>
			</view>
		</view>
		<!-- 订单信息 -->
		<view class="order-info">
			<view class="list" @click="onSkipOrder(1)">
				<view class="icon">
					<text class="iconfont icon-qianbao"></text>
					<text v-if="userInfo.status_0>0" class="num">{{userInfo.status_0}}</text>
				</view>
				<view class="title">
					<text>拍卖订单</text>
				</view>
			</view>
			<view class="list" @click="onSkipOrder(2)">
				<view class="icon">
					<text class="iconfont icon-daifahuo"></text>
					<text v-if="userInfo.status_1>0" class="num">{{userInfo.status_1}}</text>
				</view>
				<view class="title">
					<text>参拍日志</text>
				</view>
			</view>
			<view class="list" @click="onSkipOrder(3)">
				<view class="icon">
					<text class="iconfont icon-daishouhuo"></text>
					<text v-if="userInfo.status_2>0" class="num">{{userInfo.status_2}}</text>
				</view>
				<view class="title">
					<text>委拍日志</text>
				</view>
			</view>
			<view class="list" @click="onSkipOrder(4)">
				<view class="icon">
					<text class="iconfont icon-guanzhu"></text>
				</view>
				<view class="title">
					<text>拍卖收益</text>
				</view>
			</view>
			<view class="list" @click="onSkipOrder(0)">
				<view class="icon">
					<text class="iconfont icon-daipingjia"></text>
					<!-- <text class="num">22</text> -->
				</view>
				<view class="title">
					<text>提货订单</text>
				</view>
			</view>
			<!-- 			<view class="list" @click="onSkipOrder(5)">
				<view class="icon">
					<text class="iconfont icon-tuikuan"></text>
					<text v-if="userInfo.countAfterSale>0" class="num">{{userInfo.countAfterSale}}</text>
				</view>
				<view class="title">
					<text>退换</text>
				</view>
			</view> -->
		</view>
		<!-- 钱包 -->
		<view class="wallet-info">
			<view class="list" @click="amountLog(1)">
				<view class="icon">
					<text class="number">{{userInfo.amount}}</text>
				</view>
				<view class="title">
					<text>可用余额</text>
				</view>
			</view>
			<view class="list" @click="amountLog(2)">
				<view class="icon">
					<text class="number">{{userInfo.amountPHK}}</text>
				</view>
				<view class="title">
					<text>参拍货款</text>
				</view>
			</view>
			<view class="list" @click="amountLog(0)">
				<view class="icon">
					<text class="number">{{userInfo.amountYB}}</text>
				</view>
				<view class="title">
					<text>拍豆</text>
				</view>
			</view>
			<view class="list">
				<view class="icon">
					<text class="number">{{userInfo.amountFreeze}}</text>
				</view>
				<view class="title">
					<text>冻结余额</text>
				</view>
			</view>
		</view>
		<view class="wallet-info">
			<view class="list" @click="amountLog(3)">
				<view class="icon">
					<text class="number">{{userInfo.amountHK}}</text>
				</view>
				<view class="title">
					<text>结拍货款</text>
				</view>
			</view>
			<view class="list" @click="amountLog(4)">
				<view class="icon">
					<text class="number">{{userInfo.amountZL}}</text>
				</view>
				<view class="title">
					<text>助力金</text>
				</view>
			</view>
			<view class="list" @click="amountLog(5)">
				<view class="icon">
					<text class="number">{{userInfo.amountING}}</text>
				</view>
				<view class="title">
					<text>提现中</text>
				</view>
			</view>
			<view class="list" @click="amountLog(1)">
				<view class="icon">
					<text class="iconfont icon-qianbao"></text>
				</view>
				<view class="title">
					<text class="action">钱包日志</text>
				</view>
			</view>
		</view>
		<!-- 团队 -->
		<!-- 		<view class="wallet-info">
			<view class="list" @click="myTeam(1)">
				<view class="icon">
					<text class="number">{{userInfo.users}}</text>
				</view>
				<view class="title">
					<text>注册人数</text>
				</view>
			</view>
			<view class="list" @click="myTeam(2)">
				<view class="icon">
					<text class="number">{{userInfo.usersNumber}}</text>
				</view>
				<view class="title">
					<text>正式会员</text>
				</view>
			</view>
			<view class="list">
				<view class="icon">
					<text class="number">{{userInfo.usersAmount}}</text>
				</view>
				<view class="title">
					<text>下单金额</text>
				</view>
			</view>
			<view class="list" @click="myTeam(1)">
				<view class="icon">
					<text class="iconfont icon-zhuyi"></text>
				</view>
				<view class="title">
					<text class="action">我的团队</text>
				</view>
			</view>
		</view> -->
		<!-- 积分，付款码 -->
		<view class="integral-payment">
			<!-- <view class="list" @click="onWallet('SignIn')">
				<view class="title">
					<text class="iconfont icon-qiandao" style="font-weight: bold;"></text>
					<text>签到</text>
				</view>
				<view class="mess">
					<text>每日签到 领取积分</text>
				</view>
			</view> -->
			<view class="list" @click="getMoney()">
				<view class="title">
					<text class="iconfont icon-qianbao" style="font-weight: bold;"></text>
					<text>转账和提现</text>
				</view>
				<view class="mess">
					<text>钱包互转和银行卡提现</text>
				</view>
			</view>
			<view class="list" @click="onMmeberVip">
				<view class="title">
					<text class="iconfont icon-fukuanma"></text>
					<text>推广二维码</text>
				</view>
				<view class="mess">
					<text>一起分享全新购物模式</text>
				</view>
			</view>
		</view>

		<!-- 我的服务 -->
		<view class="my-service">
			<view class="title">
				<text>我的服务</text>
			</view>
			<view class="service-list">
				<view class="list" @click="onServer('feedback')">
					<view class="thumb">
						<text class="iconfont icon-edit1" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>意见反馈</text>
					</view>
				</view>
				<view class="list" @click="onServer('serve')">
					<view class="thumb">
						<text class="iconfont icon-kefu" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>电话客服</text>
					</view>
				</view>
				<view class="list" @click="onServer('serveLink')">
					<view class="thumb">
						<text class="iconfont icon-kefu" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>微信客服</text>
					</view>
				</view>
				<view class="list" @click="onServer('about')">
					<view class="thumb">
						<text class="iconfont icon-home" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>关于我们</text>
					</view>
				</view>
			</view>
			<view class="service-list">
				<view class="list" @click="onServer('address')">
					<view class="thumb">
						<text class="iconfont icon-dingwei" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>地址管理</text>
					</view>
				</view>
				<view class="list" @click="onServer('reCharge')">
					<view class="thumb">
						<text class="iconfont icon-daishouhuo" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>充值截图</text>
					</view>
				</view>
				<view class="list" @click="onServer('reChargeLog')">
					<view class="thumb">
						<text class="iconfont icon-daipingjia" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>充值记录</text>
					</view>
				</view>
				<view class="list" @click="onServer('myTeam')">
					<view class="thumb">
						<text class="iconfont icon-guanzhu" style="font-size: 50rpx;"></text>
					</view>
					<view class="name">
						<text>我的团队</text>
					</view>
				</view>
			</view>
		</view>

		<view class="common-list">
			<view class="list" @click="onServer('agent')" v-if="userInfo.isAgent == 1">
				<view class="title">
					<text>代理中心</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<view class="list" @click="onServer('realMe')">
				<view class="title">
					<text>实名认证</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<view class="list" @click="myBank">
				<view class="title">
					<text>绑定银行卡</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<view class="list" @click="editPass">
				<view class="title">
					<text>修改登录密码</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<!-- #ifdef H5 -->
			<view class="list" @click="appDown">
				<view class="title">
					<text>APP下载</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<!-- #endif -->
			<view class="list" @click="clearDate">
				<view class="title">
					<text>清除缓存</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			<view class="list" @click="loginOut">
				<view class="title">
					<text>退出登录</text>
				</view>
				<view class="more">
					<text></text>
					<text class="iconfont icon-more"></text>
				</view>
			</view>
		</view>
		<!-- 为你推荐 -->
		<!--    <view class="recommend-info">
      <view class="recommend-title">
        <view class="title">
          <image src="/static/wntj_title.png" mode=""></image>
        </view>
      </view>
      <view class="goods-list">
        <view class="list" v-for="(item,index) in goodsList" @click="onSkip('goods')" :key="index">
          <view class="pictrue">
            <image :src="item.img" mode="heightFix"></image>
          </view>
          <view class="title-tag">
            <view class="tag">
              <text v-if="item.is_goods === 1">特价</text>
              {{item.name}}
            </view>
          </view>
          <view class="price-info">
            <view class="user-price">
              <text class="min">￥</text>
              <text class="max">{{item.price}}</text>
            </view>
            <view class="vip-price">
              <image src="/static/vip_ico.png"></image>
              <text>￥{{item.vip_price}}</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
		<!-- 客服热线 -->
		<view class="serve-hotline" @click="isHotline = false">
			<view class="cu-modal bottom-modal" :class="{'show':isHotline}">
				<view class="cu-dialog">
					<view class="contact-list">
						<view class="list">
							<text>呼叫客服</text>
						</view>
						<view class="list" @click="gotoMobile">
							<text style="color: #959595;">{{mobile}}</text>
						</view>
						<view class="list">
							<text>取消</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- tabbar -->
		<TabBar :tabBarShow="4"></TabBar>
	</view>
</template>

<script>
	import TabBar from '../../components/TabBar/TabBar.vue';
	export default {
		components: {
			TabBar,
		},
		data() {
			return {
				scrollTop: 0,
				isHotline: false,
				goodsList: {},
				nickName: '',
				vip: '会员',
				userInfo: {},
				mobile: getApp().globalData.config.mobile,
				api:getApp().globalData.apiUrl,
				token:getApp().globalData.token,
				levelTimeShow:false,
				agentTimeShow:false,
				daojishi:null
			};
		},
		onLoad() {
			this.$login.checkLogin({
				login: true
			})
			// this.getUserInfo()
		},
		onReady() {
			uni.hideTabBar();
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onShow() {
			this.getUserInfo()
		},
		methods: {
			editPass(){
				uni.navigateTo({
					url:"/pages/editPassword/editPassword"
				})
			},
			clearDate() {
    uni.showModal({
        title: "是否确定清理缓存数据？",
        content: "将清理本地存储、应用缓存和临时文件",
        success: (re) => {
            if (re.confirm) {
                uni.showLoading({
                    title: "清理中..."
                });

                try {
                    // 清理本地存储数据
                    uni.clearStorageSync();

                    setTimeout(() => {
                        // 隐藏加载提示
                        uni.hideLoading();

                        // 显示清理完成提示
                        uni.showToast({
                            title: '请重新登录',
                            icon: 'success',
                            duration: 1000,
                            success: () => {
                                setTimeout(() => {
                                    // 跳转到登录页面
                                    uni.navigateTo({
                                        url: "/pages/login/login"
                                    });
                                }, 1000);
                            },
                            fail: () => {
                                uni.showToast({
                                    title: '清理失败，请重试',
                                    icon: 'error'
                                });
                            }
                        });
                    }, 2000);
                } catch (error) {
                    console.error('清理缓存失败:', error);
                    // 隐藏加载提示
                    uni.hideLoading();
                    // 显示清理失败提示
                    uni.showToast({
                        title: '清理失败，请重试',
                        icon: 'error'
                    });
                }
            }
        },
        fail: (err) => {
            console.error('用户取消清理缓存:', err);
        }
    });
},

			appDown(){
				uni.navigateTo({
					url:"/pages/appDown/appDown"
				})
			},
			headImg() {
				var that = this
				uni.showActionSheet({
					// #ifndef MP-WEIXIN
					itemList: ['更换自定义头像', '设置默认头像'],
					// #endif
					// #ifdef MP-WEIXIN
					itemList: ['更换自定义头像', '设置默认头像'],
					// #endif
					// #ifdef H5
					itemList: ['更换自定义头像', '设置默认头像'],
					// #endif
					success: function(res) {
						if (res.tapIndex == 0) {
							uni.chooseImage({
								count: 1, // 允许选择的图片张数
								success: (res) => {
									var imagePath = res.tempFilePaths[0];
									var filePath = res.tempFilePaths[0]; // 保存本地图片路径
									uni.showLoading({
										title:'上传中...'
									})
									uni.uploadFile({
										url: that.api+'/api/setMyHead' + "?token=" + that.token, // 目标服务器地址
										filePath: filePath, // 要上传的文件的临时路径
										name: 'file', // 服务器接收文件的字段名，通常为 'file'，可以根据需要修改
										success: (uploadFileRes) => {
											uni.hideLoading()
											const data = JSON.parse(uploadFileRes.data); // 假设服务器返回 JSON 格式的响应
											if (data.code == 0) {
												that.userInfo.headImg = data.data.url
												uni.showToast({
													title:'更换成功'
												})
											}else{
												uni.showToast({
													title:data.msg
												})
											}
										},
										fail: (error) => {
											uni.hideLoading()
											// 这里可以处理上传失败的逻辑
										}
									});
								},
								fail: (err) => {
									console.error('选择图片失败:', err);
								}
							});
						}
						if (res.tapIndex == 1) {
							uni.request({
								url: that.api + "/api/setMyHead?token=" + getApp().globalData.token +
									"&type=def",
								success(res) {
									if(res.data.code == 0){
										that.userInfo.headImg = null
									}
									uni.showToast({
										title: res.data.msg
									})
								}
							})
						}
					},
					fail: function(res) {
						console.log(res.errMsg);
					}
				});
			},
			showLevelTime(){
				this.agentTimeShow=false
				this.levelTimeShow=true
				clearTimeout(this.daojishi)
				this.levelAndAgentTimeHide()
			},
			showAgentTime(){
				this.levelTimeShow=false
				this.agentTimeShow=true
				clearTimeout(this.daojishi)
				this.levelAndAgentTimeHide()
			},
			levelAndAgentTimeHide(){
				this.daojishi = setTimeout(()=>{
					this.levelTimeShow = false
					this.agentTimeShow = false
				},5000)
			},
			myBank() {
				uni.navigateTo({
					url: "/pages/myBank/myBank"
				})
			},
			loginOut() {
				uni.showModal({
					title: "请确认操作",
					content: "清除登录状态并退出",
					confirmText: "退出",
					success: function(re) {
						if (re.confirm) {
							uni.setStorageSync('token', null)
							uni.showModal({
								title: "已清除缓存并退出",
								content: "点击其他页面将自动重新登录",
								showCancel: false
							})
							setTimeout(() => {
								uni.navigateTo({
									url: "/pages/login/login"
								})
							}, 1000)
						}
					}
				})
			},
			gotoMobile() {
				window.open("tel:" + this.mobile, "_self")
			},
			gotoService() {
				// console.log(this.api + getApp().globalData.config.kefuLink)
				uni.previewImage({
					urls: [this.api + getApp().globalData.config.kefuLink] // 需要预览的图片http链接列表
				});
				// window.open(getApp().globalData.config.kefuLink, "_self")
			},
			getMoney() {
				uni.navigateTo({
					url: "/pages/amount/amount"
				})
			},
			myTeam($cint) {
				var number = 0
				if ($cint == 2) {
					number = this.userInfo.usersNumber
				} else {
					number = this.userInfo.users
				}
				uni.navigateTo({
					url: "/pages/Team/Team?number=" + number + "&cint=" + $cint
				})
			},
			amountLog(cint) {
				uni.navigateTo({
					url: "/pages/ConsumeRecord/ConsumeRecord?amount=" + this.userInfo.amount + "&cid=" + cint
				})
			},
			getUserInfo() {
				uni.showLoading({
					title: "加载中..."
				})
				this.$http.get('getUserInfo', {
					token: getApp().globalData.token
				}).then(res => {
					uni.hideLoading()
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.userInfo = res.data
						getApp().globalData.level = this.userInfo.level
						for (var i in this.userInfo.count) {
							var d = this.userInfo.count[i]
							if (d.status == 0) {
								this.userInfo.status_0 = d.count
							}
							if (d.status == 1) {
								this.userInfo.status_1 = d.count
							}
							if (d.status == 2) {
								this.userInfo.status_2 = d.count
							}
						}
						if(res.data.setPassword==false){
							var timestamp = Date.now()
							var setTimestamp = uni.getStorageSync('timestamp')
							if(setTimestamp > timestamp) return false
							uni.showModal({
								title:"未设置登录密码",
								content:'设置登录密码后更快捷登录',
								confirmText:"去设置",
								cancelText:'延迟一天',
								confirmColor:"#fe3b0f",
								success(re) {
									if(re.confirm){
										uni.navigateTo({
											url:"/pages/editPassword/editPassword"
										})
									}else{
										timestamp = timestamp + (24*3600*1000)
										uni.setStorageSync("timestamp", timestamp)
									}
								}
							})
						}
					} else {
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			/**
			 * 关注跳转
			 */
			onCollect(type) {
				switch (type) {
					case 'goods':
						uni.navigateTo({
							url: '/pages/GoodsOn/GoodsOn'
						})
						break;
					case 'content':
						uni.navigateTo({
							url: '/pages/ContentCollection/ContentCollection'
						})
						break;
					case 'record':
						uni.navigateTo({
							url: '/pages/BrowsingHistory/BrowsingHistory'
						})
						break;
				}
			},
			/**
			 * 订单
			 */
			onSkipOrder(type) {
				if (type === 5) {
					uni.navigateTo({
						url: '/pages/AfterSalesOrder/AfterSalesOrder',
					})
					return;
				}
				if (type === 1) {
					uni.navigateTo({
						url: '/pages/MyAuctionList/MyAuctionList',
					})
					return;
				}
				if (type === 2) {
					uni.navigateTo({
						url: '/pages/AuctionLog/AuctionLog?cid=1',
					})
					return;
				}
				if (type === 3) {
					uni.navigateTo({
						url: '/pages/AuctionLog/AuctionLog?cid=2',
					})
					return;
				}
				if (type === 4) {
					uni.navigateTo({
						url: '/pages/AuctionLog/AuctionLog?cid=3',
					})
					return;
				}
				uni.navigateTo({
					url: '/pages/MyOrderList/MyOrderList?type=' + type,
				})
			},
			/**
			 * 钱包跳转点击
			 */
			onWallet(type) {
				switch (type) {
					case 'integral':
						uni.navigateTo({
							url: '/pages/IntegralDetails/IntegralDetails',
						})
						break;
					case 'coupon':
						uni.navigateTo({
							url: '/pages/MyCoupon/MyCoupon',
						})
						break;
					case 'wallet':
						uni.navigateTo({
							url: '/pages/MyWallet/MyWallet',
						})
						break;
					case 'SignIn':
						uni.navigateTo({
							url: '/pages/SignIn/SignIn',
						})
						break;
					case 'payment':
						uni.navigateTo({
							url: '/pages/PaymentCode/PaymentCode',
						})
						break;
				}
			},
			/**
			 * 我的服务点击
			 */
			onServer(type) {
				switch (type) {
					case 'agent':
						uni.navigateTo({
							url: '/pages/Agent/Agent'
						})
						break;
					case 'feedback':
						uni.navigateTo({
							url: '/pages/Feedback/Feedback'
						})
						break;
					case 'serve':
						this.isHotline = true;
						break;
					case 'serveLink':
						this.gotoService()
						break;
					case 'about':
						uni.navigateTo({
							url: "/pages/AboutUs/AboutUs"
						})
						break;
					case 'address':
						uni.navigateTo({
							url: "/pages/AddressList/AddressList"
						})
						break;
					case 'reCharge':
						uni.navigateTo({
							url: "/pages/reCharge/reCharge"
						})
						break;
					case 'reChargeLog':
						uni.navigateTo({
							url: "/pages/ConsumeRecord/ConsumeRecord?cid=0"
						})
						break;
					case 'myTeam':
						uni.navigateTo({
							url: "/pages/Team/Team"
						})
						break;
					case 'about1':
						uni.navigateTo({
							url: "/pages/ArticleDetails/ArticleDetails"
						})
						break;
					case 'realMe':
						uni.navigateTo({
							url: "/pages/RealMe/RealMe"
						})
						break;
				}
			},
			/**
			 * 设置点击
			 */
			onSetting() {
				var that = this
				uni.showModal({
					title: '设置昵称',
					editable: true,
					placeholderText: "请输入昵称",
					success(re) {
						if (re.confirm) {
							if (re.content == '') {
								uni.showToast({
									title: "不可为空"
								})
								return false;
							}
							console.log(re.confirm)
							that.$http.post('saveUserInfo', {
								token: getApp().globalData.token,
								nickName: re.content
							}).then(res => {
								uni.stopPullDownRefresh();
								if (res.code == 0) {
									that.userInfo.nickName = re.content
								} else {
									uni.showToast({
										title: res.msg
									})
								}
							})
						}
					}
				})
				// uni.navigateTo({
				// 	url: '/pages/Setting/Setting'
				// })
			},
			/**
			 * 消息点击
			 */
			onMessage() {
				uni.navigateTo({
					url: '/pages/Message/Message'
				})
			},
			/**
			 * 会员点击
			 */
			onMmeberVip() {
				uni.navigateTo({
					url: '/pages/MembersOpened/MembersOpened',
				})
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type) {
				switch (type) {
					case 'goods':
						uni.navigateTo({
							url: '/pages/GoodsDetails/GoodsDetails',
							animationType: 'zoom-fade-out',
							animationDuration: 200
						})
						break;
				}
			},
			/**
			 * 用户信息点击
			 * @param {Number} type
			 */
			onUserInfo() {
				// uni.navigateTo({
				//   url: '/pages/login/login'
				// })
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'my.scss';
</style>
