<template>
	<view @click="isMore = false">
		<view class="goods-head" :style="'background:rgba(255,255,255,' + PageScrollTop / 100 + ')'">
			<!-- 返回 -->
			<view class="back" @click="onBack">
				<view class="back-one" :class="{ action: PageScrollTop > 120 }">
					<text></text>
				</view>
			</view>
			<!-- tab切换 -->
			<view class="head-tab" v-if="PageScrollTop > 120">
				<view class="tab" :class="{'action':TabShow===0}" @click="onTab(0)">
					<text>商品</text>
					<text class="line"></text>
				</view>

				<view class="tab" :class="{'action':TabShow===2}" @click="onTab(2)">
					<text>详情</text>
					<text class="line"></text>
				</view>
			</view>
			<!-- 分享更多 -->
			<view class="share-more">
				<view class="share-more-one" :class="{ action: PageScrollTop > 120 }">
					<view class="list">
						<text class="iconfont icon-share"></text>
					</view>
					<view class="list" @click.stop="isMore = !isMore">
						<text class="iconfont icon-diandian"></text>
					</view>
				</view>
				<view class="mroe-list" v-show="isMore">
					<navigator class="list">
						<view class="icon">
							<text class="iconfont icon-xiaoxi"></text>
						</view>
						<view class="title">
							<text>消息</text>
						</view>
					</navigator>
					<navigator open-type="switchTab" url="/pages/home/<USER>" class="list">
						<view class="icon">
							<text class="iconfont icon-home"></text>
						</view>
						<view class="title">
							<text>首页</text>
						</view>
					</navigator>
				</view>
			</view>
		</view>
		<!-- banner，标题 -->
		<view class="banner-title">
			<!-- banner -->
			<view class="banner">
				<swiper class="screen-swiper round-dot" indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item, index) in goodsArr.swiperList" :key="index">
						<image :src="item" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
			</view>
			<!-- 价格 -->
			<view class="price-info" v-show="type==0">
				<view class="price">
					<text class="min">￥</text>
					<text class="max">{{goodsArr.price1}}</text>
					<text class="min" style="font-size: 24rpx;font-weight: unset;margin-left: 10rpx;">
						<view>+{{goodsArr.percent}}%</view>
						<view>+{{goodsArr.percentPrice}}</view>
					</text>
				</view>
				<view class="info">
					<view class="list" @click="onAttention">
						<text class="iconfont"
							:class="AttentionShow===0?'icon-guanzhu-off':'icon-guanzhu-on action'"></text>
						<text>{{ AttentionShow === 0 ? '关注' : '已关注' }}</text>
					</view>
				</view>
			</view>
			<!-- 限时抢购 -->
			<view class="flash-price">
				<view class="price-item" style="width: 100%;">
					<view class="price"
						style="flex-direction: row;justify-content: space-between;width: 100%;align-items: center;">
						<view class="current-price" style="width: 50%;text-align: center;flex-direction: column;">
							<text class="min">委拍时间:</text>
							<text class="max">{{goodsArr.sellTime}}</text>
							<!-- <text class="min">.00</text> -->
						</view>
						<view class="current-price s-pos-rel"
							style="width: 50%;text-align: center;flex-direction: column;">
							<text class="min">参拍时间:</text>
							<text class="max">{{goodsArr.auctionTime}}</text>
							<view class="s-sort-boder"></view>
						</view>
					</view>
				</view>
			</view>
			<!-- 标题 -->
			<view class="goods-title">
				<text>{{goodsArr.title}}</text>
			</view>
		</view>
		<!-- 属性规格 -->
		<view class="goods-discounts">
			<view class="list" style="font-weight: bold;font-size: 120%;">基础信息</view>
			<view class="list">
				<view class="title">参数</view>
				<view class="content">
					<text>{{goodsArr.parameter}}</text>
				</view>
				<view class="more"><!-- <text class="iconfont icon-more"></text> --></view>
			</view>
			<view class="list">
				<view class="title">付款时间</view>
				<view class="content">
					<text>竞拍成功 落锤即付</text>
				</view>
				<view class="more"></view>
			</view>
			<view class="list s-flex s-flex-bt" style="justify-content: space-between;background-color: #efefef;">
				<view class="title s-width25">数量</view>
				<view class="content s-width25" style="justify-content: flex-end;margin-right: 20rpx;">
					<text>{{goodsArr.numbers}}</text>
				</view>
				<view class="title s-width25-2" style="border-left: 2px solid #fff;text-indent: 10px;">起拍价</view>
				<view class="content s-width25" style="justify-content: flex-end;">
					<text>￥{{goodsArr.price}}</text>
				</view>
			</view>
			<view class="list s-flex s-flex-bt" style="justify-content: space-between;background-color: #fff;">
				<view class="title s-width25">递增</view>
				<view class="content s-width25-2" style="justify-content: flex-end;margin-right: 20rpx;">
					<text>{{goodsArr.percent}}%</text>
				</view>
				<view class="title s-width25-2" style="border-left: 2px solid #fff;text-indent: 10px;">提货价</view>
				<view class="content s-width25" style="justify-content: flex-end;">
					<text>￥{{goodsArr.price2}}</text>
				</view>
			</view>
			
			<view class="list s-flex s-flex-bt" style="justify-content: space-between;background-color: #efefef;">
				<view class="title s-width25">数量</view>
				<view class="content s-width25" style="justify-content: flex-end;margin-right: 20rpx;">
					<text>{{goodsArr.numbers}}</text>
				</view>
				<view class="title s-width25-2" style="border-left: 2px solid #fff;text-indent: 10px;">市场价</view>
				<view class="content s-width25" style="justify-content: flex-end;">
					<text>￥{{goodsArr.price3}}</text>
				</view>
			</view>
			<view class="list" @click="$refs['GoodsServe'].show(goodsArr.auction)">
			  <view class="title">上拍方</view>
			  <view class="content">
			    <view class="serve">
			      <text class="iconfont icon-baozheng"></text>
			      <text>{{goodsArr.actionTitle}}</text>
			    </view>
			  </view>
			  <view class="more">
			    <text class="iconfont icon-more"></text>
			  </view>
			</view>
			<view class="list" style="font-weight: bold;font-size: 120%;">历史落锤价格</view>
			<view class="list s-flex" style="justify-content: space-between;background-color: #efefef;">
				<view style="text-align:center;width: 50%;">日期</view>
				<view style="text-align:center;width: calc(50% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">价格</view>
			</view>
			<view class="list s-flex" style="justify-content: space-between;" :style="index % 2 == 0?'background-color: #fff;':'background-color: #efefef;'" v-for="(item,index) in goodsArr.priceList" :key="index">
				<view style="text-align:center;width: 50%;">{{item.date}}</view>
				<view style="text-align:center;width: calc(50% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">{{item.price}}</view> 
			</view>
		</view>
		
		
		<!-- 商品介绍 -->
		<view class="products-introduction" ref="products">
			<view class="title">
				<text>商品介绍</text>
			</view>
			<view class="content" v-html="goodsArr.content"></view>
		</view>
		<!-- 底部 -->
		<view class="page-footer">
			<view class="footer-fn" @click="gotoService">
				<view class="list">
					<text class="iconfont icon-kefu"></text>
					<text>客服</text>
				</view>
			</view>
			<view class="footer-buy">
				<view class="buy-at" @click="$refs['GoodsAttr'].show(4)">
					<text>委拍</text>
				</view>
			</view>
			<view class="footer-buy">
				<view class="buy-at buy-at2" @click="$refs['GoodsAttr'].show(3)">
					<text>参拍</text>
				</view>
			</view>
		</view>
		<!-- 服务弹窗 -->
		<goods-serve ref="GoodsServe" :id="goodsArr.auction"></goods-serve>
		<!-- 优惠券 -->
		<goods-coupon ref="GoodsCoupon"></goods-coupon>
		<!-- 属性规格 -->
		<goods-attr ref="GoodsAttr" :goodsArr="goodsArr" :token="token"></goods-attr>
	</view>
</template>

<script>
	import GoodsServe from '../../components/GoodsServe/GoodsServe.vue';
	import GoodsCoupon from '../../components/GoodsCoupon/GoodsCoupon.vue';
	import GoodsAttr from '../../components/GoodsAttr/GoodsAttr.vue';

	export default {
		components: {
			GoodsServe,
			GoodsCoupon,
			GoodsAttr,
		},
		data() {
			return {
				TabShow: 0,
				number: 1,
				isMore: false,
				AttentionShow: 0,
				swiperList: [],
				web_content: '',
				PageScrollTop: 0,
				type: 0,
				goodsArr: {},
				goodsID: 0,
				token:getApp().globalData.token,
				load:0,
				round:0,
			};
		},
		onLoad(params) {
			// this.$login.checkLogin({login:true})
			this.type = params.type || 0;
			this.goodsID = params.id
			this.round = params.round
			this.getGoods()
		},
		onPageScroll(e) {
			this.PageScrollTop = e.scrollTop;
		},
		methods: {
			gotoService() {
				window.open(getApp().globalData.config.kefuLink, "_self")
			},
			getGoods() {
				this.load = 1
				uni.showLoading({
					title: "加载中..."
				})
				var that = this
				this.$http.get('getGoodsInfo', {
					id: that.goodsID,
					token: getApp().globalData.token,
					round:that.round
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.goodsArr = res.data
						this.goodsArr.round = that.round
					} else {
						uni.showToast({
							title: res.msg
						})
						this.status = 'noMore'
					}
				})
			},
			/**
			 * 返回
			 */
			onBack() {
				uni.navigateBack();
			},
			/**
			 * tab
			 */
			onTab(type) {
				this.TabShow = type;
				switch (type) {
					case 0:
						uni.pageScrollTo({
							scrollTop: 0,
							duration: 300
						});
						break;
					case 1:
						uni.createSelectorQuery().select(".evaluate-data").boundingClientRect((data) => { //data - 各种参数
							uni.pageScrollTo({
								scrollTop: this.PageScrollTop + data.top - 50,
								duration: 300
							});
						}).exec()
						break;
					case 2:
						uni.createSelectorQuery().select(".products-introduction").boundingClientRect((
						data) => { //data - 各种参数
							uni.pageScrollTo({
								scrollTop: this.PageScrollTop + data.top - 50,
								duration: 300
							});
						}).exec()
						break;
				}
			},
			/**
			 * 去购物车
			 */
			onToCart() {
				uni.switchTab({
					url: '/pages/cart/cart'
				})
			},
			/**
			 * 降价通知点击
			 */
			onDepreciate() {
				uni.showToast({
					title: '降价通知提醒成功',
					icon: 'success'
				})
			},
			/**
			 * 关注点击
			 */
			onAttention() {
				if (this.AttentionShow === 0) {
					this.AttentionShow = 1;
					uni.showToast({
						title: '关注成功',
						icon: 'none'
					})
				} else {
					this.AttentionShow = 0;
					uni.showToast({
						title: '取消成功',
						icon: 'none'
					})
				}

			},
			/**
			 * 评价点击
			 */
			onEvaluate() {
				uni.navigateTo({
					url: '/pages/GoodsEvaluateList/GoodsEvaluateList'
				})
			}
		}
	};
</script>

<style scoped lang="scss">
	@import 'GoodsDetails.scss';
</style>