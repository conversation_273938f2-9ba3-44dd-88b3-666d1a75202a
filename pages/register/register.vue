<template>
	<view class="page">
		<view class="logo">
			<image src="../../static/logo.png" mode=""></image>
		</view>
		<!-- 填写区 -->
		<view class="input-info">
			<view class="info">
				<input type="tel" v-model="form.phone" maxlength="11" placeholder="手机号">
				<view class="more">

				</view>
			</view>

			<view class="info">
				<input :password='!isPassword' maxlength="26" v-model="form.password" placeholder="请输入密码">
				<view class="more">
					<text class="iconfont" :class="isPassword?'icon-eye-on':'icon-eye-off'"
						@click="isPassword = !isPassword"></text>
				</view>
			</view>
			<view class="info">
				<input :password='!isPassword2' maxlength="26" v-model="form.password2" placeholder="请重复输入密码">
				<view class="more">
					<text class="iconfont" :class="isPassword2?'icon-eye-on':'icon-eye-off'"
						@click="isPassword2 = !isPassword2"></text>
				</view>
			</view>
		</view>
		<!-- 按钮 -->
		<view class="btn-info">
			<view class="btn" :style="isRegister?'opacity:1':'opacity:0.4'" @click="isRegister?onRegister():''">
				<text>注册</text>
			</view>
		</view>
		<!-- 操作 -->
		<view class="operation">
			<text></text>
			<text @click="onLogin">已有账号?登录</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isPassword: false,
				isPassword2: false,
				isRegister: false,
				fromID:'',
				// 表单
				form: {
					phone: '',
					code: '',
					password: '',
				},
			};
		},
		onLoad(option) {
			this.fromID = uni.getStorageSync('fromID')
			if(!this.fromID){
				this.fromID = option.userCode
				if(this.fromID){
					uni.setStorageSync("fromID",this.fromID)
				}
			}
			
		},
		methods: {
			onLogin() {
				uni.redirectTo({
					url: '/pages/login/login'
				})
			},
			/**
			 * 注册点击
			 */
			onRegister() {
				if(this.form.password.length<6){
					uni.showToast({
						title:"密码至少6位"
					})
					return false
				}
				if(this.form.password != this.form.password2){
					uni.showToast({
						title:"两次密码不一致"
					})
					return false
				}
				this.$http.get('regMobilePassword', {
					token: getApp().globalData.token,
					mobile:this.form.phone,
					password:this.form.password,
					fromID:this.fromID
				}).then(res => {
					
				})
			}
		},
		watch: {
			form: {
				handler(newValue, oldValue) {
					if (newValue.phone && newValue.password2 && newValue.password) {
						this.isRegister = true;
					} else {
						this.isRegister = false;
					}
				},
				deep: true
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'register.scss';
</style>