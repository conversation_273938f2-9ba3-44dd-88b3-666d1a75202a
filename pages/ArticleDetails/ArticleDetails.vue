<template>
	<view class="page">
		<!-- banner -->
		<view class="banner" v-if="articleArr.files && articleArr.files.length>0">
			<swiper class="screen-swiper round-dot" indicator-dots="true" circular="true" autoplay="true" interval="5000" duration="500">
				<swiper-item v-for="(item, index) in articleArr.files" :key="index">
					<image :src="item" mode="widthFix"></image>
					<!-- <video src="{{item.url}}" autoplay loop muted show-play-btn="{{false}}" controls="{{false}}" objectFit="cover" wx:if="{{item.type=='video'}}"></video> -->
				</swiper-item>
			</swiper>
		</view>
		<!-- 文章内容 -->
		<view class="article-data" style="overflow-y: auto;height: 100%;">
			<view class="article-title">
				<text>{{articleArr.title}}</text>
			</view>
			<view class="content" v-html="articleArr.content"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				swiperList: [
					{
						id: 0,
						type: 'image',
						url: '/static/img/banner_01.png'
					},
					{
						id: 1,
						type: 'image',
						url: '/static/img/banner_02.png'
					},
					{
						id: 2,
						type: 'image',
						url: '/static/img/banner_03.png'
					},
					{
						id: 3,
						type: 'image',
						url: '/static/img/banner_04.png'
					},
					{
						id: 4,
						type: 'image',
						url: '/static/img/banner_01.png'
					},
					{
						id: 5,
						type: 'image',
						url: '/static/img/banner_01.png'
					}
				],
				isComment: false,
				isGoods: false,
				id:0,
				articleArr:{}
			};
		},
		onLoad(params) {
			this.id = params.id
			this.getArticleInfo()
		},
		methods:{
			getArticleInfo(){
				var that = this
				this.$http.get('getArticleInfo', { 
					id: that.id
				}).then(res => {
					if (res.code == 0) {
						that.articleArr = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'ArticleDetails.scss';
</style>
