<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 首次可修改代理城市</view>
				<view style="width: 100%;">* 请注意代理期限</view>
			</view>
		</view>
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>姓名</text>
				</view>
				<view class="content">
					<input disabled="" type="text" v-model="postData.nickName" placeholder="请填姓名">
				</view>
			</view>

			<view class="list-input">
				<view class="title">
					<text>代理城市</text>
				</view>
				<view class="content">
					<input disabled type="agentCite" v-model="postData.agentCite" placeholder="请填写代理城市"  @click="open">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>代理区县</text>
				</view>
				<view class="content">
					<input disabled type="agent" v-model="postData.agent" placeholder="请填写代理城市"  @click="open">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>代理期限</text>
				</view>
				<view class="content">
					<input disabled="" type="agentTime" v-model="postData.agentTime" placeholder="请填写代理期限">
				</view>
			</view>
		</view>
		<view class="address-input">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 代理区域业绩按收货地址标记</view>
			</view>
		</view>
		<view class="list-input" v-if="1==2">

			<view class="list s-flex" style="justify-content: space-between;background-color: #efefef;">
				<view style="text-align:center;width: 33%;line-height: 100rpx;">月份</view>
				<view style="text-align:center;width: calc(33% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">业绩</view>
				<view style="text-align:center;width: calc(34% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">单量</view>
			</view>
			<view class="list s-flex" style="justify-content: space-between;" :style="index % 2 == 0?'background-color: #fff;':'background-color: #efefef;'" v-for="(item,index) in postData.priceList" :key="index">
				<view style="text-align:center;width: 33%;line-height: 100rpx;">{{item.year}}-{{item.month}}</view>
				<view style="text-align:center;width: calc(33% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">{{item.amount}}</view>
				<view style="text-align:center;width: calc(34% - 2px);border-left: 2px solid #fff;height: 100%;line-height: 100rpx;">{{item.numbers}}</view>
			</view>
		</view>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress">
				<text>确定</text>
			</view>
		</view>
		<cityPicker :column="column" :default-value="defaultValue" :mask-close-able="maskCloseAble" @confirm="confirm" @cancel="cancel" :visible="visible"/>
	</view>
</template>

<script>
	import cityPicker from '@/uni_modules/piaoyi-cityPicker/components/piaoyi-cityPicker/piaoyi-cityPicker'
	export default {
		components: {
		    cityPicker
		},
		data() {
			return {
				img1:'',
				img2:'',
				visible: false,
				maskCloseAble: true,
				str: '',
				column: 3,
				
				defaultValue: '110105',
				// defaultValue: ['河北省','唐山市','丰南区'],
				column: 3,
				addressType: '2',
				postData: {
					username:'',
					numbers:'',
					img1:'',
					img2:''
				},
				tagAction1: 'action',
				tagAction2: '',
				tagAction3: '',
				checked: false,
				id: 0,
				city: '', //记录区县 便于代理统计
				imagePath:''
			};
		},
		onLoad(params) {
			this.$login.checkLogin({
				login: true
			})
			this.addressType = params.type || '2';
			uni.setNavigationBarTitle({
				title: this.addressType === '1' ? '代理中心' : '代理中心'
			})
			this.id = params.id
			this.getAddressInfo()
		},
		methods: {
			upImg() {
				if(this.postData.img1!='' && this.postData.img2!=''){
					uni.showToast({
						title:"最多2张图片"
					})
					return false
				}
				uni.chooseImage({
					count: 1, // 只选一张
					sizeType: ['original', 'compressed'], // 可以指定原图或压缩图
					sourceType: ['album', 'camera'], // 可以指定来源是相册或相机
					success: (res) => {
						// 选择图片成功
						this.imagePath = res.tempFilePaths[0];
						this.uploadImage()
					},
					fail: (err) => {
						console.log('选择图片失败', err);
					}
				})
			},
			uploadImage() {
				if (!this.imagePath) {
					uni.showToast({
						title: '请先选择图片',
						icon: 'none'
					});
					return;
				}

				uni.uploadFile({
					url: this.$http.baseUrl+"upload",
					filePath: this.imagePath,
					name: 'file', // 后端接收文件的字段名
					formData: {
						// 可以带一些额外参数
						token: getApp().globalData.token
					},
					success: (uploadRes) => {
						// 上传成功
						var re = JSON.parse(uploadRes.data)
						if(this.postData.img1==''){
							this.postData.img1 = getApp().globalData.apiUrl + re.data.url
						}else{
							this.postData.img2 = getApp().globalData.apiUrl + re.data.url
						}
					},
					fail: (err) => {
						console.log('上传失败', err);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				});
			},
			saveAddress() {
				this.$http.post('agentSave', {
					token: getApp().globalData.token,
					data: JSON.stringify(this.postData),
					id: this.id
				}).then(res => {
					uni.showToast({
						title: res.msg
					})
				})
			},
			getAddressInfo() {
				this.$http.get('getUserInfo', {
					token: getApp().globalData.token,
					agent:1
				}).then(res => {
					if (res.code == 0) {
						this.postData = res.data
					} else {

					}
				})
			},
			selectTag(cint, str) {
				this.clearAction()
				this.postData.tagID = cint
				this.postData.tag = str
				if (cint == 1) {
					this.tagAction1 = 'action'
				}
				if (cint == 2) {
					this.tagAction2 = 'action'
				}
				if (cint == 3) {
					this.tagAction3 = 'action'
				}
			},
			open() {
				this.visible = true
			},
			confirm (val) {
			    console.log(val)
			    this.str = val
				this.postData.agent = val.areaName
				this.postData.agentCode = val.code
				this.postData.agentCite = val.provinceName + val.cityName
			    this.visible = false
			},
			cancel() {
				this.visible = false
			}
		},
	}
</script>

<style scoped lang="scss">
	@import '../Feedback/Feedback.scss';
	@import 'Agent.scss';
</style>