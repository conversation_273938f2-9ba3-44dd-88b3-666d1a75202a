<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>手机号</text>
				</view>
				<view class="content">
					<input type="text" disabled="" v-model="postData.mobile" placeholder="请填写手机号">
				</view>
			</view>
			<block v-if="forget">
				<view class="list-input">
					<view class="title">
						<text>验证码</text>
					</view>
					<view class="content s-flex s-flex-bt">
						<input type="code" style="width: 40vw;" v-model="postData.code" placeholder="请填验证码">
						<text @click="getCode">{{getCodeTxt}}</text>
					</view>
				</view>
			</block>
			<block v-if="postData.setPassword==false">

			</block>
			<block v-else>
				<view class="list-input" v-if="!forget">
					<view class="title">
						<text>旧密码</text>
					</view>
					<view class="content">
						<input type="text" v-model="postData.password" placeholder="请填写旧密码">
					</view>
				</view>
				<view @click="forget=!forget" style="line-height: 80rpx;width: 100vw;padding: 0 4vw;text-align: right;color: #fe3b0f;">{{forget?'用旧密码验证':'忘记旧密码？'}}</view>
			</block>
			
			<view class="list-input">
				<view class="title">
					<text>新密码</text>
				</view>
				<view class="content">
					<input type="safe-password" v-model="postData.password1" placeholder="请填写新密码">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>重复密码</text>
				</view>
				<view class="content">
					<input type="safe-password" v-model="postData.password2" placeholder="请重复密码">
				</view>
			</view>
		</view>
		<view class="footer-btn">
			<view class="btn" @click="savePassword">
				<text>保存</text>
			</view>
		</view>
		<cityPicker :column="column" :default-value="defaultValue" :mask-close-able="maskCloseAble" @confirm="confirm"
			@cancel="cancel" :visible="visible" />
	</view>
</template>

<script>
	import cityPicker from '@/uni_modules/piaoyi-cityPicker/components/piaoyi-cityPicker/piaoyi-cityPicker'
	export default {
		components: {
			cityPicker
		},
		data() {
			return {
				forget:false,
				visible: false,
				maskCloseAble: true,
				str: '',
				defaultValue: '110105',
				// defaultValue: ['河北省','唐山市','丰南区'],
				column: 3,
				addressType: '2',
				postData: {
					tagID: 1,
					tag: '家',
					default: 1
				},
				tagAction1: 'action',
				tagAction2: '',
				tagAction3: '',
				checked: false,
				getCodeTxt: '获取验证码',
				id: 0,
				city: '', //记录区县 便于代理统计
				timeOut: 60,
				gotoTimeOut: true,
				token:getApp().globalData.token
			};
		},
		onLoad(params) {
			this.$login.checkLogin({
				login: true
			})
			this.getUserInfo()
		},
		methods: {
			savePassword() {
				this.$http.get('savePassword', {
					password1: this.postData.password1,
					password2: this.postData.password2,
					password: this.postData.password,
					token:this.token,
					forget:this.forget,
					code:this.postData.code
				}).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: "保存成功"
						})
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					} else {
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			daojishi() {
				setTimeout(() => {
					this.getCodeTxt = this.timeOut + "秒后获取"
					this.timeOut--
					if (this.timeOut > 0) {
						this.daojishi()
					} else {
						this.getCodeTxt = '获取验证码'
						this.timeOut = 60
						this.gotoTimeOut = false
					}
				}, 1000)
			},
			
			getCode() {
				var that = this
				if (that.timeOut != 60) {
					uni.showToast({
						title: "倒计时结束获取"
					})
					return false
				}
				uni.showLoading({
					title: '获取中...'
				})
				if (that.gotoTimeOut == true) {

					that.gotoTimeOut = false
					setTimeout(() => {
						this.$http.get('getCode2', {
							mobile: that.postData.mobile
						}).then(res => {
							uni.hideLoading()
							if (res.code == 0) {
								uni.showToast({
									title: '发送成功'
								})
								that.daojishi()
							} else {
								that.gotoTimeOut = true
								uni.showToast({
									title: res.msg
								})
							}
						})
					}, 1000)
					// that.daojishi()
				} else {
					uni.showToast({
						title: that.timeOut + "秒后获取"
					})
				}
			},
			getUserInfo() {
				this.$http.get('getUserInfo', {
					token: getApp().globalData.token,
				}).then(res => {
					if (res.code == 0) {
						this.postData = res.data
						if (this.postData.default == 1) {
							this.checked = true
						}
						this.clearAction()
						if (this.postData.tagID == 1) {
							this.tagAction1 = 'action'
						}
						if (this.postData.tagID == 2) {
							this.tagAction2 = 'action'
						}
						if (this.postData.tagID == 3) {
							this.tagAction3 = 'action'
						}
					} else {

					}
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	@import 'editPassword.scss';
</style>