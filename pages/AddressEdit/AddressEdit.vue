<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>收货人</text>
				</view>
				<view class="content">
					<input type="text" v-model="postData.username" placeholder="请填写收货人姓名">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>手机号</text>
				</view>
				<view class="content">
					<input type="tel" v-model="postData.mobile" placeholder="请填写收货人手机号">
				</view>
			</view>
			<view class="list-input" @click="open">
				<view class="title">
					<text>所在地区</text>
				</view>
				<view class="content s-flex s-flex-bt" style="width: 100%;">
					<view style="padding-left: 20rpx;">{{postData.address1}}</view>
					<text class="iconfont icon-more" style="color: #ccc;"></text>
					<!-- <input disabled="" type="text" v-model="postData.address1" placeholder="省市区县/乡镇等" @click="open"> -->
				</view>
			</view>
			<view class="list-textarea">
				<view class="title">
					<text>详细地址</text>
				</view>
				<view class="content">
					<textarea v-model="postData.address2" type="tel" placeholder="街道/楼牌号等" />
				</view>
			</view>
		</view>
		<view class="tag-default">
			<view class="tag-list">
				<view class="title">
					<text>标签</text>
				</view>
				<view class="content">
					<view :class="'list '+tagAction1" @click="selectTag(1,'家')">
						<text>家</text>
					</view>
					<view :class="'list '+tagAction2" @click="selectTag(2,'公司')">
						<text>公司</text>
					</view>
					<view :class="'list '+tagAction3" @click="selectTag(3,'学校')">
						<text>学校</text>
					</view>
				</view>
			</view>
			<view class="default-address">
				<view class="title">
					<text>默认地址</text>
				</view>
				<view class="switch-default">
					<switch class="red sm" color="#0077EE !important" :checked="checked" @change="selectDefault"></switch>
				</view>
			</view>
		</view>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress">
				<text>保存</text>
			</view>
		</view>
		<cityPicker :column="column" :default-value="defaultValue" :mask-close-able="maskCloseAble" @confirm="confirm" @cancel="cancel" :visible="visible"/>
	</view>
</template>

<script>
	import cityPicker from '@/uni_modules/piaoyi-cityPicker/components/piaoyi-cityPicker/piaoyi-cityPicker'
	export default {
		components: {
		    cityPicker
		},
		data() {
			return {
				visible: false,
				maskCloseAble: true,
				str: '',
				defaultValue: '110105',
				// defaultValue: ['河北省','唐山市','丰南区'],
				column: 3,
				addressType: '2',
				postData:{tagID:1,tag:'家',default:1},
				tagAction1:'action',
				tagAction2:'',
				tagAction3:'',
				checked:false,
				id:0,
				city:'',//记录区县 便于代理统计
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login:true})
			this.addressType = params.type||'2';
			uni.setNavigationBarTitle({
				title: this.addressType === '1' ? '编辑收货地址':'新建收货地址'
			})
			this.id = params.id
			if(this.id>0){
				this.getAddressInfo()
			}
		},
		methods: {
			saveAddress(){
				this.$http.post('saveAddress', { 
					token: getApp().globalData.token,
					data:JSON.stringify(this.postData),
					id:this.id
				}).then(res => {
					uni.stopPullDownRefresh();
					uni.showToast({
						title:res.msg
					})
					if (res.code == 0) {
						setTimeout(function(){
							uni.navigateBack()
						},1000)
					} else {

					}
				})
			},
			getAddressInfo(){
				this.$http.get('getAddressInfo', {
					token: getApp().globalData.token,
					id:this.id
				}).then(res => {
					if (res.code == 0) {
						this.postData=res.data
						if(this.postData.default==1){
							this.checked=true
						}
						this.clearAction()
						if(this.postData.tagID==1){
							this.tagAction1='action'
						}
						if(this.postData.tagID==2){
							this.tagAction2='action'
						}
						if(this.postData.tagID==3){
							this.tagAction3='action'
						}
					} else {
				
					}
				})
			},
			selectTag(cint,str){
				this.clearAction()
				this.postData.tagID=cint
				this.postData.tag=str
				if(cint==1){
					this.tagAction1='action'
				}
				if(cint==2){
					this.tagAction2='action'
				}
				if(cint==3){
					this.tagAction3='action'
				}
			},
			clearAction(){
				this.tagAction1='';
				this.tagAction2='';
				this.tagAction3='';
			},
			selectDefault(obj){
				var event = obj.detail
				if(event.value==true){
					this.postData.default=1
				}else{
					this.postData.default=0
				}
				
			},
			open () {
		        this.visible = true
		    },
		    confirm (val) {
		        // console.log(val)
		        this.str = val
				this.postData.address1 = val.name
				this.postData.areaName = val.areaName
				this.postData.provinceName = val.provinceName
				this.postData.cityName = val.cityName
				this.postData.code = val.code
		        this.visible = false
		    },
		    cancel () {
		        this.visible = false
		    }
		},        
	}
</script>

<style scoped lang="scss">
	@import 'AddressEdit.scss';
</style>
