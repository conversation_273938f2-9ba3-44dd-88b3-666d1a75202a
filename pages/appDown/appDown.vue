<template>
	<view class="page">
		<view class="logo">
			<image src="../../static/logo.png" mode=""></image>
		</view>
		<!-- 填写区 -->
		<view class="other-ways">
			<text>下载安卓APP</text>
		</view>
		<!-- 登录方式 -->
		<view class="login-way">
			<view class="way" @click="gotoDown">
				<image :src="api+'/static/code.png'" mode="aspectFit"></image>
				<text>点击或扫码下载</text>
			</view>
		</view>
		<view style="position: fixed;width: 100vw;height: 100vh;left: 0;top: 0;" v-if="showWX==1">
			<image :src="api+'/static/wxDown.jpg'" mode="scaleToFill" style="width: 100vw;height: 100vh;"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLogin: false,
				loginSuccess:false,
				api:getApp().globalData.apiUrl,
				isLoginWay: true,
				newUser:0,
				isPassword: false,
				getCodeTxt: '获取验证码',
				xieyiArr: [{
						title: '用户协议',
						id: 12
					},
					{
						title: '隐私声明',
						id: 13
					},
				],
				showWX:0,
				timeOut: 60,
				gotoTimeOut: true,
				agreeXY: false,
				fromID: '',
				// 表单
				form: {
					phone: '',
					code: '',
					password: '',
					password2: '',
				},
			};
		},
		onLoad(option) {
			var ua = window.navigator.userAgent.toLowerCase();
			if (ua.match(/MicroMessenger/i) == 'micromessenger') {
			   this.showWX = 1
			}
		},
		methods: {
			gotoIndex(){
				uni.switchTab({
					url:"/pages/home/<USER>"
				})
			},
			gotoDown(){
				window.open("http://html.rongyipai.cn/apk/rongyipai.apk","_self")
			},
			agree(e) {
				this.agreeXY = !this.agreeXY
			},
			gotoArticle($id) {
				uni.navigateTo({
					url: "/pages/ArticleDetails/ArticleDetails?id=" + $id
				})
			},
			onRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			/**
			 * 登录切换
			 */
			onLoginCut() {
				this.isLoginWay = !this.isLoginWay;
				// 验证码
				if (this.isLoginWay) {
					this.isLogin = this.form.code && this.form.phone ? true : false;
					this.form.password = ''
				}
				// 账号密码
				if (!this.isLoginWay) {
					this.isLogin = this.form.password && this.form.phone ? true : false;
					this.form.code = ''
				}
			},
			/**
			 * 登录点击
			 */
			onLogin() {
				if (this.agreeXY == false) {
					uni.showToast({
						title: "查看并同意协议"
					})
					return false
				}
				this.$http.get('mobileLogin', {
					mobile: this.form.phone,
					code: this.form.code,
					fromID: this.fromID,
				}).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: "登录成功"
						})
						uni.setStorageSync("token", res.msg)
						getApp().globalData.token = res.msg
						setTimeout(() => {
							if(res.data.new == 1){
								// #ifdef APP
								uni.switchTab({
									url: "/pages/home/<USER>"
								})
								// #endif
								// #ifndef APP
								this.newUser=1
								this.loginSuccess=true
								// #endif
							}else{
								uni.switchTab({
									url: "/pages/home/<USER>"
								})
							}
						}, 1000)
					} else {
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			daojishi() {
				setTimeout(() => {
					this.getCodeTxt = this.timeOut + "秒后获取"
					this.timeOut--
					if (this.timeOut > 0) {
						this.daojishi()
					} else {
						this.getCodeTxt = '获取验证码'
						this.timeOut = 60
					}
				}, 1000)
			},
			getCode() {
				var that = this
				if (that.agreeXY == false) {
					uni.showToast({
						title: "查看并同意协议"
					})
					return false
				}
				if (that.timeOut != 60) {
					uni.showToast({
						title: "倒计时结束获取"
					})
					return false
				}
				uni.showLoading({
					title: '获取中...'
				})
				if (that.gotoTimeOut == true) {

					that.gotoTimeOut = false
					setTimeout(() => {
						this.$http.get('getCode2', {
							mobile: that.form.phone
						}).then(res => {
							uni.hideLoading()
							if (res.code == 0) {
								if (that.form.phone == '13111111111') {
									that.form.code = res.msg
								}
								uni.showToast({
									title: '发送成功'
								})
								that.daojishi()
							} else {
								that.gotoTimeOut = true
								uni.showToast({
									title: res.msg
								})
							}
						})
					}, 1000)
					// that.daojishi()
				} else {
					uni.showToast({
						title: that.timeOut + "秒后获取"
					})
				}
			}
		},
		watch: {
			form: {
				handler(newValue, oldValue) {
					// 验证码
					if (this.isLoginWay) {
						this.isLogin = newValue.code && newValue.phone ? true : false;
					}
					// 账号密码
					if (!this.isLoginWay) {
						this.isLogin = newValue.password && newValue.phone ? true : false;
					}
				},
				deep: true
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'appDown.scss';
</style>