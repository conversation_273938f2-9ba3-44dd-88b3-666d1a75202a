<template>
	<view class="page">
		<view class="head-back">
			<!-- 			<view class="back" @click="onBack">
						<text></text>
					</view> -->
			<view class="title s-title-w">
				全部商品
			</view>
		</view>
		<!-- 分类列表 -->
		<view class="classify-list s-flex s-flex-bt">
			<view class="list" @click="select(0)" :style="selectID == 0?'border-bottom: red solid 2px;':''">
				<view class="s-flex" style="align-items: center;">
					<view class="thumb">
						<text class="iconfont icon-baozheng"></text>
					</view>
					<view class="name"><text class="one-omit">在拍商品</text></view>
				</view>
			</view>
			<view class="list" @click="select(1)" :style="selectID == 1?'border-bottom: red solid 2px;':''">
				<view class="s-flex" style="align-items: center;">
					<view class="thumb">
						<text class="iconfont icon-baozheng"></text>
					</view>
					<view class="name"><text class="one-omit">已拍商品</text></view>
				</view>
			</view>
			<view class="list" @click="select(2)" :style="selectID == 2?'border-bottom: red solid 2px;':''">
				<view class="s-flex" style="align-items: center;">
					<view class="thumb">
						<text class="iconfont icon-baozheng"></text>
					</view>
					<view class="name"><text class="one-omit">科技助力</text></view>
				</view>
			</view>
			<view class="list" @click="select(3)" :style="selectID == 3?'border-bottom: red solid 2px;':''">
				<view class="s-flex" style="align-items: center;">
					<view class="thumb">
						<text class="iconfont icon-baozheng"></text>
					</view>
					<view class="name"><text class="one-omit">助力区</text></view>
				</view>
			</view>
		</view>
		<!-- 更多热卖 -->
		<!-- 商品列表 -->
		<view class="goods-data">
				<view class="goods-list">
					<view :class="isList?'list-view':'list-li'" v-for="(item,index) in goodsList" @click="onSkip('goods',item.id)" :key="index">
						<view class="thumb">
							<image :src="item.img" mode="heightFix"></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{item.name}}</text>
							</view>
							<view class="price">
								<view class="retail-price">
									<text class="min">￥</text>
									<text class="max">{{item.price}}</text>
									<view class="tag" v-if="item.is_goods === 1">
										<text>抢购价</text>
									</view>
								</view>
								<view class="vip-price">
									<text class="min">￥</text>
									<text class="max">{{item.vip_price}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
		</view>
		<view v-if="status=='noMore'" style="text-align: center;color: gray;margin-bottom: 200rpx;">--- 已加载所有 ---</view>
		<view v-else style="color: gray;margin-bottom: 200rpx;">--- 加载更多 ---</view>
		<!-- tabbar -->
		<TabBar :tabBarShow="3"></TabBar>
	</view>
</template>

<script>
	import TabBar from '../../components/TabBar/TabBar.vue';
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		components: {
			TabBar,
			MescrollMixin
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {
				},
				api: getApp().globalData.apiUrl,
				rounds: [],
				selectID: 0,
				goodsList: [],
				page: 1,
				goodsList: [],
				classGoodsList: [],
				round: 0,
				index: 0,
				isList: true,
			};
		},
		onLoad() {
			// this.getRound()
			// this.getGoods()
		},
		onReachBottom() {
			this.page++
			this.getGoods()
		},
		onShow() {
			if(this.goodsList.length < 1){
				this.page=1
				this.getGoods()
			}
		},
		methods: {
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback(){
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			upCallback(page) {
				setTimeout(() =>{
					this.mescroll.endByPage(10, 20);
				},2000)
			},
			getGoods() {
				uni.showLoading({
					title: "加载中..."
				})
				var that = this
				this.$http.get('getAllGoods', {
					page: that.page,
					limit: 10,
					token: getApp().globalData.token,
					cid: that.selectID
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data.length == 0) {
							this.status = 'noMore'
						} else {
							this.goodsList = this.goodsList.concat(res.data);
							if (res.data.length < 10) {
								this.status = 'noMore'
							}
						}
					} else {
						this.status = 'noMore'
					}
				})
			},
			select(cint) {
				this.selectID = cint
				this.page = 1
				this.goodsList = []
				this.getGoods()
			},
			getRound() {
				var that = this
				this.$http.get('getRound', {

				}).then(res => {
					if (res.code == 0) {
						that.rounds = res.data
						that.round = res.data[0].id
						that.getGoods()
					}
				})
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type, id) {
				switch (type) {
					case 'classify':
						uni.navigateTo({
							url: '/pages/SearchGoodsList/SearchGoodsList',
						})
						break;
					case 'goods':
						uni.navigateTo({
							url: '/pages/ConfirmAuctionOrder/ConfirmAuctionOrder?id='+id+'&fromType=goods',
							animationType: 'zoom-fade-out',
							animationDuration: 200
						})
						break;
				}
			}
		}
	};
</script>

<style lang="scss">
	@import 'goods.scss';
</style>