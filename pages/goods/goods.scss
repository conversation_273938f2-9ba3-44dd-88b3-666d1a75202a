
.page{
	position: absolute;
	left: 0;
	top: 100rpx;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
	padding-bottom: 40rpx;
}
.head-back{
	position: relative;
	position: fixed;
	left: 0; 
	top: 0;   
	z-index: 100; 
	width: 100%;
	height: 100rpx;
	background: linear-gradient(to bottom,$base,$base);
	/* #ifdef APP-PLUS */
	height: calc(100rpx + var(--status-bar-height));
	padding-top: var(--status-bar-height);
	/* #endif */
	/* #ifdef MP */
	height: 150rpx;
	padding-top: var(--status-bar-height);
	/* #endif */
	.back{
		position: absolute;
		left: 0;
		top: 0;
		/* #ifdef APP-PLUS */
		// top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		// top: var(--status-bar-height);
		/* #endif */
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		height: 100rpx;
		text{
			display: flex;
			width: 20rpx;
			height: 20rpx;
			border-left: 2rpx solid #FFFFFF;
			border-bottom: 2rpx solid #FFFFFF;
			transform: rotate(45deg);
		}
	}
	.title{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		image{
			width: 200rpx;
			height: 50rpx;
		}
	}
}
// 分类列表
.classify-list {
	display: flex;
	flex-wrap: wrap;
	padding: 10rpx 25rpx;
	background-color: #ffffff;
	border-radius: 0 0 20rpx 20rpx;
	margin: 20rpx auto;
	margin-top: calc(20rpx + var(--status-bar-height));
	.list {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 20%;
		height: 80rpx;
		.thumb {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 70%;
			image {
				width: 40rpx;
				height: 40rpx;
			}
		}
		.name {
			margin-left: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 30%;
			text {
				font-size: 32rpx;
				color: #212121;
				font-weight: bold;
			}
		}
	}
}
// 超值爆款
.super-hot-style {
	width: 100%;
	background-color: #ffffff;
	border-radius: 20rpx;
	.hot-title {
		display: flex;
		align-items: center;
		padding: 0 4%;
		height: 80rpx;
		.iconfont {
			font-size: 38rpx;
			color: $base;
		}
		.title {
			font-size: 30rpx;
			color: #212121;
			margin-left: 10rpx;
		}
	}
	.goods-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0 4%;
		padding-bottom: 20rpx;
		.list {
			width: 32%;
			margin-right: 2%;
			.thumb {
				width: 100%;
				image {
					width: 100%;
				}
			}
			.title {
				display: flex;
				width: 100%;
				padding: 10rpx 0;
				text {
					color: #212121;
					font-size: 26rpx;
				}
			}
			.price {
				padding: 10rpx 0;
				.sales-volume {
					font-size: 24rpx;
					color: #c0c0c0;
					margin-top: 5rpx;
				}
				.retail-price {
					width: 100%;
					display: flex;
					align-items: center;
					.min {
						display: inline-block;
						font-size: 24rpx;
						color: $price-clor;
						transform: scale(0.8);
					}
					.max {
						font-size: 26rpx;
						color: $price-clor;
						font-weight: bold;
					}
				}
				.vip-price {
					display: flex;
					align-items: center;
					.min {
						display: inline-block;
						font-size: 24rpx;
						color: #212121;
					}
					.max {
						font-size: 24rpx;
						color: #212121;
						font-weight: bold;
						margin-right: 10rpx;
					}
				}
			}
		}
		.list:nth-child(3n) {
			margin-right: 0;
		}
	}
}
/* 商品列表 */
.goods-data {
	width: 100%;
	margin-top: 20rpx;
	/* #ifdef APP-PLUS */
	// margin-top: 270rpx;
	/* #endif */
	/* #ifdef MP */
	// margin-top: calc(320rpx + var(--status-bar-height));
	/* #endif */
	.goods-list {
		padding: 0 25rpx;
		border-radius: 20rpx;
		overflow: hidden;
		.list-view {
			float: left;
			width: 49%;
			height: 560rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			margin-right: 2%;
			margin-bottom: 20rpx;
			overflow: hidden;
			.thumb {
				width: 100%;
				//height: 300rpx;
				overflow: hidden;
				image {
                    height: 350rpx;
				}
			}
			.item {
				width: 100%;
				.title {
					padding: 20rpx;
					text {
						width: 100%;
						color: #212121;
						font-size: 26rpx;
					}
				}
				.price {
					padding: 0 20rpx;
					.retail-price {
						display: flex;
						align-items: flex-end;
						width: 100%;
						height: 40rpx;
						.min {
							display: inline-block;
							font-size: 24rpx;
							color: $base;
							font-weight: bold;
							transform: scale(0.7);
						}
						.max {
							font-size: 28rpx;
							color: $base;
							font-weight: bold;
						}
						.tag {
							position: relative;
							background-color: $base;
							border-radius: 4rpx;
							margin-left: 10rpx;
							text {
								display: inline-block;
								color: #ffffff;
								font-size: 24rpx;
								transform: scale(0.7);
							}
						}
						.tag:before {
							position: absolute;
							left: -6rpx;
							top: 0;
							content: '';
							width: 0;
							height: 0;
							border-top: 0rpx solid transparent;
							border-right: 10rpx solid $base;
							border-bottom: 6rpx solid transparent;
						}
					}
					.vip-price {
						display: flex;
						align-items: flex-end;
						width: 100%;
						height: 40rpx;
						.min {
							display: inline-block;
							font-size: 24rpx;
							color: #212121;
						}
						.max {
							font-size: 24rpx;
							color: #212121;
						}
					}
				}
			}
		}
		.list-view:nth-child(2n) {
			margin-right: 0;
		}
		// 列表
		.list-li {
			display: flex;
			align-items: center;
			width: 100%;
			height: 300rpx;
			background-color: #ffffff;
			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;
				image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 10rpx;
				}
			}
			.item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				width: 70%;
				height: 100%;
				border-bottom: 2rpx solid #f6f6f6;
				.title {
					padding: 20rpx;
					text {
						width: 100%;
						color: #212121;
						font-size: 26rpx;
					}
				}
				.price {
					padding: 0 20rpx;
					.retail-price {
						display: flex;
						align-items: flex-end;
						width: 100%;
						height: 40rpx;
						.min {
							display: inline-block;
							font-size: 24rpx;
							color: $base;
							font-weight: bold;
							transform: scale(0.7);
						}
						.max {
							font-size: 28rpx;
							color: $base;
							font-weight: bold;
						}
						.tag {
							position: relative;
							background-color: $base;
							border-radius: 4rpx;
							margin-left: 10rpx;
							text {
								display: inline-block;
								color: #ffffff;
								font-size: 24rpx;
								transform: scale(0.7);
							}
						}
						.tag:before {
							position: absolute;
							left: -6rpx;
							top: 0;
							content: '';
							width: 0;
							height: 0;
							border-top: 0rpx solid transparent;
							border-right: 10rpx solid $base;
							border-bottom: 6rpx solid transparent;
						}
					}
					.vip-price {
						display: flex;
						align-items: flex-end;
						width: 100%;
						height: 40rpx;
						.min {
							display: inline-block;
							font-size: 24rpx;
							color: #212121;
						}
						.max {
							font-size: 24rpx;
							color: #212121;
						}
					}
				}
			}
		}
	}
}
// 更多热卖
.more-hot {
	width: 100%;
	background-color: #f2f2f2;
	.hot-title {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		.title {
			font-size: 32rpx;
			color: $base;
			.icon {
				margin: 0 20rpx;
			}
		}
	}
  .goods-list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 10rpx;
    .list{
      width: 49%;
      height: 540rpx;
      margin-bottom: 20rpx;
      background-color: #FFFFFF;
      border-radius: 10rpx;
      overflow: hidden;
      .pictrue{
        display: flex;
        justify-content: center;
        width: 100%;
        image{
          height: 350rpx;
        }
      }
      .title-tag{
        // display: flex;
        height: 100rpx;
        padding: 20rpx;
        .tag{
          float: left;
          margin-right: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;
          font-size: 26rpx;
          line-height: 40rpx;
          text{
            font-size: 24rpx;
            color: #FFFFFF;
            padding: 4rpx 16rpx;
            background: linear-gradient(to right,$base,$change-clor);
            border-radius: 6rpx;
            margin-right: 10rpx;
          }
        }
      }
      .price-info{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        height: 80rpx;
        .user-price{
          display: flex;
          align-items: center;
          text{
            color: $price-clor;
          }
          .min{
            font-size: 24rpx;
          }
          .max{
            font-size: 32rpx;
          }
        }
        .vip-price{
          display: flex;
          align-items: center;
          image{
            width: 26rpx;
            height: 26rpx;
            margin-right: 10rpx;
          }
          text{
            color: #fcb735;
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
