.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}
/* 反馈类型 */
.feedback-type{
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 94%;
	height: 100rpx;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 0 4%;
	.title{
		display: flex;
		align-items: center;
		text{
			font-size: 26rpx;
			color: #959595;
		}
	}
	.picker{
		position: absolute;
		width: 100%;
		height: 100%;
		opacity: 0;
	}
	.more{
		display: flex;
		align-items: center;
		text{
			color: #222222;
			font-size: 34rpx;
		}
	}
}

/* 反馈内容 */
.feedback-data{
	width: 94%;
	// height: 600rpx;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	.content{
		padding: 20rpx;
		textarea{
			width: 100%;
			height: 320rpx;
			background-color: #f6f6f6;
			border-radius: 20rpx;
			padding: 20rpx;
			font-size: 26rpx;
			color: #222222;
		}
	}
	.voucher-img{
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		height: 240rpx;
		margin-top: 20rpx;
		.list{
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 100%;
					text{
						font-size: 24rpx;
						color: #555555;
					}
					.iconfont{
						font-size: 42rpx;
						color: #212121;
					}
				}
	}
}

/* 联系方式 */
.contact-way{
	display: flex;
	align-items: center;
	justify-content: center;
	width: 94%;
	height: 100rpx;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	input{
		width: 100%;
		height: 100%;
		padding: 0 4%;
		font-size: 26rpx;
		color: #222222;
	}
}

/* 提交 */
.submit-btn{
	display: flex;
	align-items: center;
	justify-content: center;
	width: 94%;
	height: 80rpx;
	margin: 30rpx auto;
	background: linear-gradient(to right,$base,$change-clor);
	border-radius: 80rpx;
	box-shadow: 0 10rpx 10rpx $base;
	text{
		color: #FFFFFF;
		font-size: 28rpx;
	}
}