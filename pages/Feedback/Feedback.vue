<template>
	<view class="page">
		<!-- 选择反馈类型 -->
		<view class="feedback-type">
			<view class="title">
				<text>{{TypeArray[TypeIndex]}}</text>
			</view>
			<view class="picker">
				<picker @change="FeedbackTypeCh" :value="TypeIndex" :range="TypeArray">
						<view class="uni-input">{{TypeArray[TypeIndex]}}</view>
				</picker>
			</view>
			<view class="more">
				<text class="iconfont icon-more1"></text>
			</view>
		</view>
		<!-- 反馈内容 -->
		<view class="feedback-data" style="height: 360rpx;">
			<view class="content">
				<textarea v-model="content" value="" placeholder="请输入反馈的内容" />
			</view>
		</view>
		<!-- 联系方式 -->
		<view class="contact-way">
			<input type="text" v-model="mobile" placeholder="手机/邮箱/QQ">
		</view>
		<!-- 提交 -->
		<view class="submit-btn" @click="postFeedBack">
			<text>提交</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				TypeArray: ['商品相关', '物流状况', '客户服务', '优惠活动','产品体验','产品功能','其他问题'],
				TypeIndex: 0,
				content:'',
				mobile:''
			};
		},
		onLoad() {
			this.$login.checkLogin({login:true})
		},
		methods:{
			postFeedBack(){
				var that = this
				uni.showModal({
					title:'是否确定提交',
					success:function(re){
						if(re.confirm){
							that.$http.post('postFeedBack', {
								token: getApp().globalData.token,
								content:that.content,
								mobile:that.mobile,
								ctype:that.TypeArray[that.TypeIndex]
							}).then(res => {
								uni.stopPullDownRefresh();
								if (res.code == 0) {
									uni.showToast({
										title:"谢谢您的反馈"
									})
									setTimeout(function(){
										uni.navigateBack()
									},1500)
								} else {
									uni.showToast({
										title:res.msg
									})
								}
							})
						}
					}
				})
			},
			/**
			 * 反馈类型
			 * @param {Object} val
			 */
			FeedbackTypeCh(val){
				
				this.TypeIndex = val.detail.value;
				
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'Feedback.scss';
</style>
