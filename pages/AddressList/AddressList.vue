<template>
	<view class="page">
		<!-- 地址列表 -->
		<view class="address-list">
			<view class="list" @click="selectID(item.id)" v-for="(item,index) in addressList" :key="index">
				<view class="name-phone">
					<view class="name">
						<text class="one-omit">{{item.username}}</text>
					</view>
					<view class="phone">
						<text>{{item.mobile}}</text>
						<text class="tag" v-if="item.default==1">默认</text>
						<text class="tag blue">{{item.tag}}</text>
					</view>
				</view>
				<view class="address-edit">
					<view class="address">
						<text>{{item.address1}}{{item.address2}}</text>
					</view>
					<view class="edit" @click.stop="onAddressEdit(1,item.id)">
						<text class="iconfont icon-edit1"></text>
					</view>
				</view>
			</view>
		</view>
		<!-- 添加地址 -->
		<view class="add-address">
			<view class="btn" @click="onAddressEdit(2)">
				<text>新建收货地址</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList:{}
			};
		},
		onLoad() {
			this.$login.checkLogin({login:true})
		},
		onShow() {
			this.getList()
		},
		methods:{
			selectID(cid){
				if(cid>0){
					for(var i in this.addressList){
						var d = this.addressList[i]
						if(d.id == cid){
							getApp().globalData.selectAddress=d
							uni.navigateBack()
						}
					}
				}
			},
			getList(){
				var that = this
				this.$http.get('getAddressList', { 
					token: getApp().globalData.token
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.addressList = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
						this.status = 'noMore'
					}
				})
			},
			/**
			 * 编辑地址点击
			 */
			onAddressEdit(type,cid){
				uni.navigateTo({
					url: '/pages/AddressEdit/AddressEdit?type=' + type+"&id="+cid,
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'AddressList.scss';
</style>
