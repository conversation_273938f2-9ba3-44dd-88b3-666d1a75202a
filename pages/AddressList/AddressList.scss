.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #FFFFFF;
}

/* 地址列表 */
.address-list{
	width: 100%;
	background-color: #FFFFFF;
	padding-bottom: 120rpx;
	.list{
		padding: 0 4%;
		height: 160rpx;
		border-bottom: 2rpx solid #f6f6f6;
		.name-phone{
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;
			.name{
				display: flex;
				align-items: center;
				width: 30%;
				height: 100%;
				text{
					width: 100%;
					font-size: 26rpx;
					font-weight: bold;
					color: #222222;
				}
			}
			.phone{
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;
				text{
					font-size: 28rpx;
					font-weight: bold;
					color: #222222;
				}
				.tag{
					padding: 4rpx 8rpx;
					font-size: 24rpx;
					color: #FFFFFF;
					background-color: $base;
					border-radius: 4rpx;
					margin-left: 20rpx;
				}
				.blue{
					background-color: #0099FF;
				}
			}
		}
		.address-edit{
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 60rpx;
			.address{
				display: flex;
				align-items: center;
				width: 90%;
				height: 100%;
				text{
					font-size: 26rpx;
					color: #959595;
				}
			}
			.edit{
				display: flex;
				align-items: center;
				justify-content: flex-end;
				width: 10%;
				height: 100%;
				text{
					font-size: 38rpx;
					color: #555555;
				}
			}
		}
	}
}

/* 添加地址 */
.add-address{
	position: fixed;
	left: 0;
	bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100rpx;
	.btn{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 80%;
		height: 70rpx;
		background: linear-gradient(to right,$base,$change-clor);
		border-radius: 70rpx;
		box-shadow: 0 10rpx 10rpx $base;
		text{
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}