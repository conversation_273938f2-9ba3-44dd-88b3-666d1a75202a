<template>
	<view class="page">
		<view class="head-back">
<!-- 			<view class="back" @click="onBack">
				<text></text>
			</view> -->
			<view class="title s-title-w">
				拍卖公告
			</view>
		</view>
		<view class="head-bg">
			<view class="banner">
				<swiper class="screen-swiper square-dot" indicator-dots="true" circular="true" autoplay="true" interval="5000" duration="500">
					<swiper-item v-for="(item,index) in swiperList" :key="index">
						<image :src="item.url" mode="aspectFill"></image>
						<!-- <video src="{{item.url}}" autoplay loop muted show-play-btn="{{false}}" controls="{{false}}" objectFit="cover" wx:if="{{item.type=='video'}}"></video> -->
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view class="goods-data">
			<view class="goods-list">
				<view class="list" v-for="(item,index) in goodsList" 
				:key="index">
					<view class="thumb">
						<image :src="item.img" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">{{item.title}}</text>
						</view>
						<view class="introduce">
							<text class="two-omit">规格：{{item.parameter}} 
							{{item.startDate}} - {{item.endDate}}</text>
						</view>
						<view class="like-goods s-flex s-flex-bt">
							<view>起拍价:{{item.price1}}</view>
<!-- 							<view class="like">
								<text>300人预约</text>
								<text class="iconfont icon-guanzhu"></text>
							</view> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="status=='noMore'" style="text-align: center;color: gray;">--- 已加载所有 ---</view>
		<view v-else style="color: gray;">--- 加载更多 ---</view>
		<!-- tabbar -->
		<TabBar :tabBarShow="1"></TabBar>
	</view>
</template>

<script>
	import TabBar from '../../components/TabBar/TabBar.vue';
	export default {
		components: {
			TabBar
		},
		data() {
			return {
				swiperList: [],
				status:'',
				page:1,
				goodsList:[],
				api:getApp().globalData.apiUrl
			};
		},
		onLoad() {
			this.getBanner()
			this.getGoods()
		},
		onReachBottom() {
			if(this.status!='noMore'){
				this.page++
				this.getGoods()
			}
		},
		methods:{
			getGoods() {
				uni.showLoading({
					title: "加载中..."
				})
				this.$http.get('getGoodsFuture', {
					goods_category_id: 1,
					page: this.page,
					limit: 10
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data.length == 0) {
							this.status = 'noMore'
						} else {
							this.goodsList = this.goodsList.concat(res.data);
							if (res.data.length < 10) {
								this.status = 'noMore'
							}
						}
					} else {
						this.status = 'noMore'
					}
				})
			},
			getBanner(){
				var that = this
				this.$http.get('getBanner', {
					cid: 239
				}).then(res => {
					if (res.code == 0) {
						that.swiperList = res.data
					}
				})
			},
			/**
			 * 返回点击
			 */
			onBack(){
				uni.navigateBack();
			},
			/**
			 * 商品点击
			 */
			onGoods(){
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'GoodChoice.scss';
</style>
