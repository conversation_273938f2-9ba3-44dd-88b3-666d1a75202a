.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}


.activity-list{
	padding: 20rpx 4%;
	.list{
		width: 100%;
		margin-bottom: 10px;
		.date{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;
			text{
				padding: 10rpx 30rpx;
				background-color: rgba(0,0,0,0.2);
				color: #FFFFFF;
				font-size: 26rpx;
				border-radius: 10rpx;
			}
		}
		.item{
			padding: 0 4%;
			height: 500rpx;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			.title{
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				text{
					font-size: 32rpx;
					color: #222222;
				}
			}
			.pictrue{
				position: relative;
				width: 100%;
				height: 300rpx;
				image{
					width: 100%;
					height: 100%;
				}
				.hint{
					position: absolute;
					left: 0;
					top: 0;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;
					height: 100%;
					background-color: rgba(0,0,0,0.3);
					text{
						font-size: 32rpx;
						color: #FFFFFF;
					}
				}
			}
			.describe{
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				text{
					font-size: 28rpx;
					color: #959595;
				}
			}
		}
	}
}
