<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 请正确填写金额和上传截图</view>
				<view style="width: 100%;">* 只能跟本账号充值</view>
				<view style="width: 100%;">* 充值前需实名认证</view>
			</view>
		</view>
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>姓名</text>
				</view>
				<view class="content">
					<input type="text" disabled="" v-model="postData.username" placeholder="请填姓名">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>身份证号</text>
				</view>
				<view class="content">
					<input type="tel" disabled="" v-model="postData.numbers" placeholder="请填写身份证号">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>充值金额</text>
				</view>
				<view class="content">
					<input type="number" v-model="postData.amount" placeholder="请填充值金额">
				</view>
			</view>
			<picker mode="date" @change="setDate">
			<view class="list-input s-flex s-flex-bt">
				<view class="title">
					<text>充值日期</text>
				</view>
				<view class="content s-flex" style="justify-content: flex-end;">
					{{addDate}}
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			</picker>
			<picker mode="time" @change="setTime">
			<view class="list-input s-flex s-flex-bt">
				<view class="title">
					<text>充值时间</text>
				</view>
				<view class="content s-flex" style="justify-content: flex-end;">
					{{addTime}}
					<text class="iconfont icon-more"></text>
				</view>
			</view>
			</picker>
		</view>
		<view class="address-input">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 请上传JPG或PNG图片</view>
				<view style="width: 100%;">* 每张图片保持在3M以内</view>
			</view>
		</view>
		<view class="feedback-data">
			<view class="voucher-img">
				<view class="list" @click="upImg" style="width: 30%;margin-right: 3%;">
					<image src="/static/up_img.png"></image>
				</view>
				<view class="list" style="width: 30%;margin-right: 3%;">
					<image :src="postData.img1"></image>
				</view>
				<view class="list" style="width: 30%;">
					<image :src="postData.img2"></image>
				</view>
			</view>
			<button @click="postData.img1='';postData.img2=''">清空图片</button>
		</view>
		
			<view class="feedback-data" v-if="postData.status==0">{{postData.content}}</view>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress">
				<text>提交充值信息</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				img1:'',
				img2:'',
				visible: false,
				maskCloseAble: true,
				str: '',
				defaultValue: '510105',
				// defaultValue: ['河北省','唐山市','丰南区'],
				column: 3,
				addressType: '2',
				postData: {
					username:'',
					numbers:'',
					img1:'',
					addDate:'',
					addTime:'',
					img2:''
				},
				tagAction1: 'action',
				tagAction2: '',
				tagAction3: '',
				checked: false,
				id: 0,
				city: '', //记录区县 便于代理统计
				imagePath:'',
				addDate:'',
				addTime:''
			};
		},
		onLoad(params) {
			this.$login.checkLogin({
				login: true
			})
			this.addressType = params.type || '2';
			this.id = params.id
			this.getAddressInfo()
		},
		methods: {
			setDate(e){
				this.addDate = e.detail.value
				this.postData.addDate = e.detail.value
			},
			setTime(e){
				this.addTime = e.detail.value
				this.postData.addTime = e.detail.value
			},
			upImg() {
				if(this.postData.img1!=''){
					uni.showToast({
						title:"最多1张图片"
					})
					return false
				}
				uni.chooseImage({
					count: 1, // 只选一张
					sizeType: ['original', 'compressed'], // 可以指定原图或压缩图
					sourceType: ['album', 'camera'], // 可以指定来源是相册或相机
					success: (res) => {
						// 选择图片成功
						this.imagePath = res.tempFilePaths[0];
						this.uploadImage()
					},
					fail: (err) => {
						console.log('选择图片失败', err);
					}
				})
			},
			uploadImage() {
				if (!this.imagePath) {
					uni.showToast({
						title: '请先选择图片',
						icon: 'none'
					});
					return;
				}

				uni.uploadFile({
					url: this.$http.baseUrl+"upload",
					filePath: this.imagePath,
					name: 'file', // 后端接收文件的字段名
					formData: {
						// 可以带一些额外参数
						token: getApp().globalData.token
					},
					success: (uploadRes) => {
						// 上传成功
						var re = JSON.parse(uploadRes.data)
						if(this.postData.img1==''){
							this.postData.img1 = getApp().globalData.apiUrl + re.data.url
						}else{
							this.postData.img2 = getApp().globalData.apiUrl + re.data.url
						}
					},
					fail: (err) => {
						console.log('上传失败', err);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				});
			},
			saveAddress() {
				var that=this
				if(this.postData.amount <= 0 || this.postData.amount == undefined){
					uni.showToast({
						title:"金额不可为空"
					})
					return false
				}
				if(this.postData.img1==''){
					uni.showToast({
						title:"图片不可为空"
					})
					return false
				}
				uni.showModal({
					title:"是否确定提交?",success(re) {
						if(re.confirm){
							that.$http.post('reChargeSave', {
								token: getApp().globalData.token,
								data: JSON.stringify(that.postData),
								id: that.id
							}).then(res => {
								uni.showToast({
									title: res.msg
								})
							})
						}
					}
				})
				
			},
			getAddressInfo() {
				this.$http.get('realMeGet', {
					token: getApp().globalData.token,
				}).then(res => {
					if (res.code == 0) {
						this.postData = res.data
						this.postData.img1='';
						this.postData.img2=''
					} else {

					}
				})
			},
			selectTag(cint, str) {
				this.clearAction()
				this.postData.tagID = cint
				this.postData.tag = str
				if (cint == 1) {
					this.tagAction1 = 'action'
				}
				if (cint == 2) {
					this.tagAction2 = 'action'
				}
				if (cint == 3) {
					this.tagAction3 = 'action'
				}
			},
			open() {
				this.visible = true
			},
			cancel() {
				this.visible = false
			}
		},
	}
</script>

<style scoped lang="scss">
	@import '../Feedback/Feedback.scss';
	@import 'reCharge.scss';
</style>