<template>
	<view class="page">
		<image style="width: 100vw;height: 100vh;" mode="scaleToFill" :src="QRCode" @click="seeImg"></image>
		<!-- 背景 -->
		<view class="head-bg"  v-if="1==2">
			<view class="vip-title">
				<text>推广计划</text>
			</view>
			<view class="vip-describe">
				<text>享受永久分红</text>
			</view>
		</view>
		<!-- 会员权益 -->
		<view class="vip-interests" v-if="1==2">
			<view class="user-info" style="display: flex;justify-content: center;">
			  <view class="portrait" style="display: flex;justify-content: start;justify-items: center;align-items: center;">
						<image v-if="userInfo.headImg=='' || userInfo.headImg==null" src="/static/head.png" ></image>
						<image v-else :src="userInfo.headImg"></image>
			  </view>
			  <view class="info">
			    <view class="nickname">
			      <text>{{userInfo.nickName}}</text>
			    </view>
			    <view class="nickname">
						<text class="iconfont icon-vip">{{userInfo.levelStr}}</text>
			    </view>
			  </view>
			</view>
			<view style="display: flex;justify-content: center;height: 1000rpx;">
				<image style="width: 520rpx;margin-top: 40rpx;height: 100%;" mode="aspectFit" :src="QRCode" @click="seeImg"></image>
			</view>
			
			<view class="dredge-btn" @click="makeQRCode">
				<text>立即生成专属二维码</text>
			</view>
		</view>
		<view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				level:0,
				QRCode:getApp().globalData.config.myQRCode,
				nickName:'',
				vip:'会员',
				userInfo:{},
			};
		},
		onLoad() {
			this.$login.checkLogin({login:true})
			this.level = getApp().globalData.level,
			this.getUserInfo()
			this.makeQRCode()
		},
		methods:{
			seeImg(){
				uni.previewImage({
					urls: [this.QRCode] // 需要预览的图片http链接列表
				});
			},
			makeQRCode(){
				uni.showLoading({
					title:"生成中"
				})
				this.$http.get('makeQRCode', {
					token: getApp().globalData.token
				}).then(res => {
					setTimeout(function(){
						uni.hideLoading()
						uni.showToast({
							title:res.msg
						})
					},1000)
					if (res.code == 0) {
						this.QRCode = res.data.myQRcode
					}
					if (res.code == 2) {
						uni.showModal({
							content:res.msg,
							confirmText:"去创建",
							success(res) {
								if(res.confirm){
									uni.navigateTo({
										url:"/pages/myBank/myBank"
									})
								}
							}
						})
					}
				})
			},
			getUserInfo(){
				this.$http.get('getUserInfo', {
					token: getApp().globalData.token
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.userInfo = res.data
						getApp().globalData.level = this.userInfo.level
						for(var i in this.userInfo.count){
							var d = this.userInfo.count[i]
							if(d.status==0){
								this.userInfo.status_0=d.count
							}
							if(d.status==1){
								this.userInfo.status_1=d.count
							}
							if(d.status==2){
								this.userInfo.status_2=d.count
							}
						}
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	@import 'MembersOpened.scss';
	.user-info{
	  display: flex;
	  align-items: center;
	  width: 100%;
	  height: 80rpx;
	  .thumb{
	    width: 60rpx;
	    height: 60rpx;
	    image{
	      width: 100%;
	      height: auto;
	      border-radius: 100%;
	    }
	  }
	  .nickname-grade{
	    width: 100%;
	    height: 60rpx;
	    margin-left: 20rpx;
	    .nickname{
	      display: flex;
	      align-items: center;
	      text{
	        font-size: 24rpx;
	        color: #212121;
	      }
	    }
	    .grade{
	      display: flex;
	      justify-content: space-between;
	      align-items: center;
	      width: 100%;
	      margin-top: 6rpx;
	      .star{
	        display: flex;
	        align-items: center;
	        text{
	          font-size: 24rpx;
	          color: $base;
	        }
	      }
	      .date{
	        display: flex;
	        align-items: center;
	        text{
	          font-size: 24rpx;
	          color: #999999;
	        }
	      }
	    }
	  }
	}
	.portrait{
		width: 15%;
		height: 100%;
		image{
			width: 60rpx;
			height: 60rpx;
			border-radius: 100%;
		}
	}
</style>
