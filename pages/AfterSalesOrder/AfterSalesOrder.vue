<template>
	<view class="page">
		<!-- 订单tab -->
		<view class="order-tab">
			<view class="tab" :class="{'action':OrderType==-1}" @click="onOrderTab(-1)">
				<text>售后申请</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==-2}" @click="onOrderTab(-2)">
				<text>处理中</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==-3}" @click="onOrderTab(-3)">
				<text>售后退货</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==-4}" @click="onOrderTab(-4)">
				<text>已完成</text>
				<text class="line"></text>
			</view>
		</view>
		<!-- 订单搜索 -->
<!-- 		<view class="order-search">
			<view class="search">
				<text class="iconfont icon-fadajing"></text>
				<input type="text" placeholder="商品名称/商品编号/订单号/序列号">
			</view>
			<view class="filtrate" @click="isFiltrate = true">
				<text>筛选</text>
			</view>
		</view> -->
		<!-- 订单列表 -->
		<view class="order-list">
			<view class="list" v-for="(item,index) in ordersList" :key="index">
				<view class="order-number">
					<view class="number">
						<text>服务单号：{{item.payShopNumbers}}</text>
					</view>
					<view class="type">
						<image src="/static/sale_th.png" mode=""></image>
						<text>{{item.statusStr}}</text>
					</view>
				</view>
				<view class="goods-list">
					<view class="list">
						<view class="thumb">
							<image :src="item.goodsImg" mode=""></image>
						</view>
						<view class="item">
							<view class="title">
								<text class="two-omit">{{item.goodsTitle}}</text>
							</view>
							<view class="num">
								<text>数量：{{item.number}}</text> <text style="margin-left: 40rpx;">金额：{{item.afterSellAmount}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="order-status" v-if="OrderType == -1">
					<text>审核中</text>
					<text>当前订单退货审核中</text>
				</view>
<!-- 				<view class="order-btn" v-if="OrderType === 0">
					<text class="action">申请售后</text>
				</view> -->
			</view>
		</view>
		<!-- 筛选弹窗 -->
		<view class="filtrate-win" @click="isFiltrate = false">
			<view class="cu-modal drawer-modal justify-end" :class="{'show':isFiltrate}">
			  <view class="cu-dialog basis-lg">
			    <view class="order-time">
						<view class="title">
							<text>下单时间</text>
						</view>
						<view class="time-list">
							<view class="list action">
								<text>全部</text>
							</view>
							<view class="list">
								<text>一个月内</text>
							</view>
							<view class="list">
								<text>一个月至三个月</text>
							</view>
							<view class="list">
								<text>三个月六个月</text>
							</view>
							<view class="list">
								<text>六个月至一年</text>
							</view>
							<view class="list">
								<text>一年以上</text>
							</view>
						</view>
					</view>
					<view class="footer-btn">
						<view class="btn" @click="isFiltrate = false">
							<text>重置</text>
						</view>
						<view class="btn action" @click="isFiltrate = false">
							<text>确定</text>
						</view>
					</view>
			  </view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				OrderType: -1,
				isFiltrate: false,
				ordersList:{}
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login:true})
			this.OrderType = params.type;
			if(this.OrderType==null || this.OrderType==undefined){
				this.OrderType=-1
			}
			this.getOrderList()
		},
		methods:{
			getOrderList(){
				var that = this
				this.$http.get('getOrderList', { 
					token: getApp().globalData.token,
					OrderType:that.OrderType
				}).then(res => {
					if (res.code == 0) {
						that.ordersList = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 订单tab切换状态
			 * @param {Number} type
			 */
			onOrderTab(type){
				this.OrderType = type;
				this.getOrderList()
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'AfterSalesOrder.scss';
</style>
