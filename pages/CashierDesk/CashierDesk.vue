<template>
	<view class="page">
		<view class="price-count-down">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">{{orderInfo.amount}}</text>
				<!-- <text class="min">.00</text> -->
			</view>
<!-- 			<view class="count-down">
				<view class="title">支付剩余时间</view>
				<view class="count">
					<text class="time">{{hour}}</text>
					<text class="dot">:</text>
					<text class="time">{{min}}</text>
					<text class="dot">:</text>
					<text class="time">{{sec}}</text>
				</view>
			</view> -->
		</view> 
		<!-- 支付方式列表 -->
		<view class="pay-way">
			<view class="pay-list">
				<view class="list" v-for="(item,index) in PayList" 
				@click="onPayWay(item,index)"
				:key="index">
					<view class="pay-type">
						<image :src="item.icon" mode=""></image>
						<text v-if="index==1">{{item.name}}({{orderInfo.myAmount}})</text>
						<text v-else>{{item.name}}</text>
					</view>
					<view class="check">
						<text class="iconfont" :class="PayWay === index ? 'icon-checked action':'icon-check'"></text>
					</view>
				</view>
			</view>
		</view>
		<view class="pay-submit">
			<view class="submit" @click="onSubmit">{{PayPirce}}{{orderInfo.amount}}</view>
		</view>
	</view>
</template>

<script>
	var script = document.createElement('script');
		script.src = "https://res.wx.qq.com/open/js/jweixin-1.6.0.js";
		document.body.appendChild(script);
	export default {
		data() {
			return {
				PayList: [
					{
						icon: '/static/wx_pay.png',
						name: '微信支付',
					},
					// {
					// 	icon: '/static/zfb_pay.png',
					// 	name: '支付宝支付',
					// },
					{
						icon: '/static/ye_pay.png',
						name: '余额支付',
					},
				],
				PayWay: 0,
				PayPirce: `微信支付￥`,
				CountDown: 1000,
				day: 0,
				hour: 0,
				min: 0,
				sec: 0,
				orderID:0,
				orderInfo:{}
			};
		},
		onLoad(params){
			// this.CountDownData();
			this.$login.checkLogin({login:true})
			this.orderID = params.orderID
			if(this.orderID<1){
				uni.showToast({
					title:"ID错误或已完成支付"
				})
			}else{
				this.getOrderInfo()
			}
		},
		methods:{
			getOrderInfo(){
				var that = this
				this.$http.get('getOrderInfo', { 
					token: getApp().globalData.token,
					orderID:that.orderID
				}).then(res => {
					if (res.code == 0) {
						that.orderInfo = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 支付方式切换点击
			 */
			onPayWay(item,index){
				this.PayWay = index;
				this.PayPirce = `${item.name}￥`
			},
			/**
			 * 倒计时
			 */
			CountDownData(){
				setTimeout(() =>{
					this.CountDown--;
					this.day = parseInt(this.CountDown / (24*60*60))
					this.hour = parseInt(this.CountDown / (60 * 60) % 24);
					this.min = parseInt(this.CountDown / 60 % 60);
					this.sec = parseInt(this.CountDown % 60);
					if(this.CountDown <= 0){
						return
					}
					this.CountDownData();
				},1000)
			},
			/**
			 * 支付点击
			 */
			onSubmit(){
				uni.showLoading({
					title:"请求中"
				})
				if(this.PayWay==1){
					if(this.orderInfo.myAmount*1 < this.orderInfo.amount*1){
						uni.showToast({
							title:"余额不足"
						})
						uni.hideLoading()
						return false;
					}
				}
				var that = this
				this.$http.post('orderPay', { 
					token: getApp().globalData.token,
					orderID:that.orderID,
					payWay:that.PayWay
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if(that.PayWay==0){
							//加载微信支付配置
							if (typeof WeixinJSBridge == "undefined") {
								if (document.addEventListener) {
									document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
								} else if (document.attachEvent) {
									document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
									document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
								}
							} else {
								res.data.type = that.PayWay
								onBridgeReady(res.data);
							}   
						}else{
							uni.showToast({
								title:res.msg
							})
							setTimeout(function(){
								uni.switchTab({
									url:"/pages/my/my"
								})
							},1500)
						}
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
				
			}
		}
	}
	
	function onBridgeReady(resx) {
	            WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
					"appId": "" + resx.appId,     //公众号ID，由商户传入
					"timeStamp": resx.timeStamp,         //时间戳，自1970年以来的秒数
					"nonceStr": "" + resx.nonceStr, //随机串
					"package": "" + resx.package,
					// "signType": "MD5",         //微信签名方式：
					"signType": "RSA",         //微信签名方式：
					"paySign": "" + resx.paySign //微信签名	
	            },
	            function (res) {
	                if (res.err_msg == "get_brand_wcpay_request:ok") {
	                    // 使用以上方式判断前端返回,微信团队郑重提示：
	                    //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
	                    var jishi = setInterval(function () {
							uni.request({
								url:getApp().globalData.apiUrl+"/api/pay_end?token="+getApp().globalData.token+"&order="+resx.payShopNumbers+"&type="+resx.type,
								success: (re) => {
									if(re.data.code == 0){
										clearInterval(jishi);
										uni.showToast({
											title:re.data.msg
										})
										setTimeout(function(){
											uni.switchTab({
												url:"/pages/my/my"
											})
										},1500)
									}else{
										// clearInterval(jishi);
										uni.showToast({
											title:re.data.msg
										})
									}
								}
							})
						}, 2000)
					}
				});
	}
	function setOtherPay(res){
		wx.config({
		  debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		  appId: ''+res.signPackage.appId, // 必填，公众号的唯一标识
		  timestamp: res.signPackage.timestamp, // 必填，生成签名的时间戳
		  nonceStr: ''+res.signPackage.nonceStr, // 必填，生成签名的随机串
		  signature: res.signPackage.signature,// 必填，签名
		  jsApiList: ['onMenuShareTimeline','updateAppMessageShareData'] // 必填，需要使用的JS接口列表
		});
		wx.ready(function () {   //需在用户可能点击分享按钮前就先调用
		  wx.updateAppMessageShareData({ 
		    title: ''+res.sharearr.title, // 分享标题
		    desc: ''+res.sharearr.des, // 分享描述
		    link: 'https://html.cdyuejia.com/#/pages/my/pay?id='+res.id, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
		    imgUrl: 'https://html.cdyuejia.com/static/img2/logo3.png', // 分享图标
		    success: function () {
		      // 设置成功
		    }
		  })
		});
	}
	
	
</script>

<style scoped lang="scss">
	@import 'CashierDesk.scss';
</style>
