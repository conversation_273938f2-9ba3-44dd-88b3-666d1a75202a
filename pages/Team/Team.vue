<template>
	<view class="page">
		<!-- 头部背景 -->
		<view class="head-bg">
			<view class="wallet-balance">
				<view class="wallet">
					<text class="number">{{amount}}</text>
					<text><text class="iconfont icon-user" style="margin-right: 20rpx;"></text>分享人数</text>
				</view>
			</view>

			<view class="bg">
				<image src="/static/integral_bg1.png" mode=""></image>
			</view>
		</view>
		<scroll-view class="record-list" @scrolltolower="lower" style="height: calc(100vh - 180rpx);" scroll-y>
			<view class="list s-flex-bt" v-for="(item,index) in amountList" :key="index">
				<view class="title-date">
					<view class="title">
						<view style="width: 46vw;">{{item.nickname}} <text
								style="font-size: 80%;color: gray;margin-left: 20rpx;font-weight: 100;">等级:{{item.levelStr}}
								{{item.agent?' 代理:'+item.agent:''}}</text></view>
					</view>
					<view class="date">
						<text>注册：{{item.addtime}}</text>
					</view>
				</view>
				<view class="title-date">
					<view class="title title2">
						<!-- <text>分享：{{item.pathNum}}</text> -->
						<text @click="call(item.mobile)" class="iconfont icon-kefu"
							style="background-color: rgba(255, 0, 0, 0.1);padding: 0rpx 20rpx;border-radius: 40rpx;line-height: 40rpx;color: #fe3b0f;">一键拨号</text>
					</view>
					<view class="date title2">
						<text v-if="item.lastAuctionDate == '1970-01-01'">最近参拍：无</text>
						<text v-else>最近参拍：{{item.lastAuctionDate}}</text>
					</view>
				</view>
			</view>
			<view v-show="noMore" class="on-anything">
				———— 没有更多了 ————
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				amountList: {},
				amount: 0,
				cint: 0, //0所有日志，1审核中，2已提现，3订单日志
				goto: true,
				noMore: false,
				selectIndex: 0,
			};
		},
		onLoad(params) {
			this.amount = params.amount
			this.cint = params.cint
			this.$login.checkLogin({
				login: true
			})
			this.selectIndex = params.cid ? params.cid : 0
			this.getUserAmount(this.selectIndex)
			// this.getUserAmount()
		},
		methods: {
			call($mobile = '') {
				// #ifdef H5
				// H5 环境
				window.location.href = 'tel:' + $mobile;
				// #endif

				// #ifdef APP-PLUS
				// App 环境
				uni.makePhoneCall({
					phoneNumber: $mobile,
					success: function() {
						console.log('success');
					},
					fail: function() {
						console.log('fail');
					}
				});
				// #endif
			},
			getUserAmount(cint) {
				this.selectIndex = cint
				this.page = 1
				this.amountList = {}
				this.getUserAmountList()
			},
			getUserAmountList(cint) {
				var that = this
				that.$http.post('getMyTeam', {
					token: getApp().globalData.token,
					page: that.page,
				}).then(res => {
					if (res.code == 0) {
						that.amountList = {
							...res.data,
							...that.amountList
						}
						if (res.data.length < 10) that.noMore = true
						that.amount = res.msg
					} else {
						if (res.data.length < 10) that.noMore = true
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			lower() {
				if (this.goto) {
					this.page++
					this.getUserAmountList()
				}
				this.goto = false
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'Team.scss';
</style>