.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}

.logo{
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 200rpx;
	image{
		width: 140rpx;
		height: 140rpx;
	}
}

.versions{
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 80rpx;
	text{
		font-size: 26rpx;
		color: #959595;
	}
}

.code{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 300rpx;
	image{
		width: 220rpx;
		height: 220rpx;
	}
	text{
		color: #959595;
		font-size: 26rpx;
		margin-top: 20rpx;
	}
}

.about-list{
	padding: 0 4%;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	margin: 20rpx auto;
	.list{
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 80rpx;
		border-bottom: 2rpx solid #f6f6f6;
		.title{
			display: flex;
			align-items: center;
			text{
				font-size: 26rpx;
				color: #222222;
			}
		}
		.more{
			display: flex;
			align-items: center;
			text{
				font-size: 26rpx;
				color: #666666;
			}
		}
	}
}

.copyright{
	position: fixed;
	left: 0;
	bottom: 0;
	display: flex;
	flex-direction:column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 200rpx;
	text{
		font-size: 26rpx;
		color: #959595;
		margin-bottom: 10rpx;
	}
}