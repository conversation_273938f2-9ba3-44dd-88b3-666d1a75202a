<template>
	<view class="page">
		<!-- 头部背景 -->
		<view class="head-bg">
<!-- 			<view class="wallet-balance">
				<view class="wallet">
					<text class="number">￥{{amount}}</text>
					<text><text class="iconfont icon-qianbao" style="margin-right: 20rpx;"></text>当前余额</text>
				</view>
			</view> -->
			<view class="s-flex s-flex-bt amount_classList">
				<view :class="selectIndex == 0?'selectClass':''" @click="getUserAmount(0)">拍豆</view>
				<view :class="selectIndex == 1?'selectClass':''" @click="getUserAmount(1)">增值</view>
				<view :class="selectIndex == 2?'selectClass':''" @click="getUserAmount(2)">参拍</view>
				<view :class="selectIndex == 3?'selectClass':''" @click="getUserAmount(3)">结拍</view>
				<view :class="selectIndex == 4?'selectClass':''" @click="getUserAmount(4)">助力金</view>
				<view :class="selectIndex == 5?'selectClass':''" @click="getUserAmount(5)">提现</view>
			</view>
			<view class="bg">
				<image src="/static/integral_bg1.png" mode=""></image>
			</view>
		</view>
		<!-- 记录列表 -->
<!-- 		<view v-if="amountList.length<1" class="on-anything">
			———— 没有任何数据 ————
		</view> -->
		<scroll-view class="record-list" @scrolltolower="lower" style="height: calc(100vh - 120rpx);" scroll-y>
			<view class="list s-flex-bt" v-for="(item,index) in amountList" :key="index">
				<view class="title-date">
					<view class="title">
						<text>{{item.title}}</text>
					</view>
					<view class="date">
						<text>{{item.addTime}}</text>
					</view>
				</view>
				<view class="title-date">
					<view class="title title2">
						<text v-if="item.amount < 0" style="color: #22AA44;">{{item.amount}}</text>
						<text v-else>+{{item.amount}}</text>
					</view>
					<view class="date title2">
						<text>{{item.status}}</text>
					</view>
				</view>
			</view>
			<view v-show="noMore" class="on-anything">
				———— 没有更多了 ————
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page:1,
				amountList:{},
				amount:0,
				cint:0,//0所有日志，1审核中，2已提现，3订单日志
				goto:true,
				noMore:false,
				selectIndex:0,
				titleArr:['拍豆日志','拍卖增值日志','参拍货款日志','结算货款日志','助力金日志',]
			};
		},
		onLoad(params) {
			this.amount = params.amount
			this.cint = params.cint
			this.$login.checkLogin({login:true})
			this.selectIndex = (params.cid===0 || params.cid>0)?params.cid:0
			if(params.cid===0 || params.cid>0){
				console.log(this.selectIndex)
				this.page=1
				this.amountList={}
			}
			this.getUserAmount(this.selectIndex)
			// this.getUserAmount()
			this.setTitle()
		},
		methods:{
			setTitle(){
				uni.setNavigationBarTitle({
					title:this.titleArr[this.selectIndex]
				})
			},
			getUserAmount(cint){
				this.setTitle()
				this.selectIndex = cint
				this.page = 1
				this.amountList={}
				this.getUserAmountList()
			},
			getUserAmountList(cint){
				var that = this
				that.$http.post('getUserAmountLog', {
					token: getApp().globalData.token,
					selectIndex:that.selectIndex * 1 + 1,
					page:that.page,
				}).then(res => {
					if (res.code == 0) {
						that.amountList = {...res.data,...that.amountList}
						if(res.data.length<10) that.noMore = true
					} else {
						if(res.data.length<10) that.noMore = true
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			lower(){
				if(this.goto){
					this.page++
					this.getUserAmountList()
				}
				this.goto=false
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'ConsumeRecord.scss';
</style>
