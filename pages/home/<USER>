<template>
	<view class="page">
		<view class="head-info">
			<!-- 搜索 -->
			<view class="head-search">
				<view class="icon-info">
					<!-- #ifdef APP -->
					<!-- <text class="iconfont icon-saoyisao"  @click="onCode"></text> -->
					<!-- #endif -->
					<!--  -->
					<image src="/static/xiaoxi_ico.png" mode="" style="position: relative;" @click="gotoMessage">
						<text v-if="messageNum>0" class="reddian tipsNum">{{messageNum}}</text>
					</image>
				</view>
				<view class="search" @click="onSearch">
					<view class="icon">
						<image src="/static/fdj_ico.png" mode=""></image>
					</view>
					<view class="hint">
						<text class="max">搜索</text>
						<text class="min">商品名称</text>
					</view>
				</view>
				<view class="icon-info" @click="onSkip('myCode')">
					<text class="iconfont icon-fukuanma"></text>
					<!-- <image src="/static/fkm_ico.png" mode=""></image> -->
				</view>
			</view>
			<!-- 分类列表 -->
<!-- 			<view class="classify-list">
				<view class="list" v-for="(item,index) in classList" :class="{'action':classifyShow==index}"
					@click="onClassify(item,index)" :key="index">
					<text>{{item.name}}</text>
					<text class="line" v-show="classifyShow==index"></text>
				</view>
			</view> -->
		</view>
		<view class="main" v-show="classifyShow===0">
				<!-- banner -->
				<view class="banner">
					<swiper class="screen-swiper square-dot" indicator-dots="true" circular="true" autoplay="true"
						interval="5000" duration="500">
						<swiper-item v-for="(item,index) in swiperList" :key="index" @click="handleNavigation(item.title2)">
							<image :src="item.url" mode="aspectFill"></image>
							<!-- <video src="{{item.url}}" autoplay loop muted show-play-btn="{{false}}" controls="{{false}}" objectFit="cover" wx:if="{{item.type=='video'}}"></video> -->
						</swiper-item>
					</swiper>
				</view>
				<!-- 菜单导航 -->
				<!-- 				<view class="menu-nav">
					<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list">
						<view class="nav" ref="nav" :style="navList.length<=10?'flex-direction:row':''">
							<view class="list" v-for="(item,index) in navList" @click="gotoLink(item.url)"
								:key="item.id">
								<image :src="'/static/nav2/'+(index+1)+'.png'" mode=""></image>
								<text>{{item.name}}</text>
							</view>
						</view>
					</scroll-view>
					<view class="indicator" v-if="navList.length>10">
						<view class="plan">
							<view class="bar" :style="'left:'+slideNum+'%'"></view>
						</view>
					</view>
				</view> -->
				<view class="integral-payment">
					<view class="list s-linear-gradient-1" @click="onWallet('tihuo')">
						<view class="title">
							<text class="iconfont icon-cart" style="font-weight: bold;"></text>
							<text>我要提货</text>
						</view>
						<view class="mess">
							<text>便捷操作不遗忘</text>
						</view>
					</view>
					<view class="list s-linear-gradient-2" @click="onWallet('tihuoLog')">
						<view class="title">
							<text class="iconfont icon-qiandao"></text>
							<text>提货记录</text>
						</view>
						<view class="mess">
							<text>随时查看不遗漏</text>
						</view>
					</view>
					<view class="list s-linear-gradient-3">
						<view class="title">
							<text class="iconfont icon-dingwei1"></text>
							<text>服务中心</text>
						</view>
						<view class="mess">
							<text>便捷高效服务</text>
						</view>
					</view>
				</view>
				<!-- 通知 -->
				<view class="inform">
					<view class="inform-info">
						<view class="picture">
							<image src="/static/gg_ico.png" mode=""></image>
						</view>
						<view class="info">
							<swiper class="swiper" :circular="true" :vertical="true" :indicator-dots="false"
								:autoplay="true" :interval="3000" :duration="1000">
								<swiper-item v-for="(item,index) in noticeList" :key="index">
									<view class="swiper-item" @click="onSkip('notice',item.id)">
										<text class="one-omit">{{item.title}} {{item.addTime}}</text>
									</view>
								</swiper-item>

							</swiper>
						</view>
					</view>
				</view>
				<view style="margin-bottom: 40rpx;">
					<image style="width: 100vw;height: 31vw;" :src="zhiboImg" @click="handleNavigation('#')"></image>
				</view>
				<!-- 为你推荐 -->
				<!-- 为你推荐 -->
				<view class="recommend-info">
					<view class="super-hot-style">
						<view class="hot-title">
							<view class="title">
								<text class="iconfont icon-xiedian"></text>
								<text class="icon">最新拍卖</text>
								<text class="iconfont icon-xiedian"></text>
							</view>
						</view>
					</view>

					<view class="goods-list">
						<view class="list" v-for="(item,index) in goodsList" @click="onSkip('goods',item.id)"
							:key="index">
							<view class="pictrue s-flex-column s-pos-rel">
								<image :src="item.img" mode="aspectFill" style="width: 100%;"></image>
								<view class="s-paiBG s-pos-abs s-flex s-flex-bt">
									<view>
										<view>委拍时间</view>
										<view>{{item.sellTime}}</view>
									</view>
									<view class="s-pos-rel">
										<view>参拍时间</view>
										<view>{{item.auctionTime}}</view>
										<view class="s-sort-boder"></view>
									</view>
								</view>
							</view>
							<view class="title-tag">
								<view class="tag">
									<!-- <text v-if="item.is_goods === 1">下单</text> -->
									{{item.name}}
								</view>
							</view>
							<view class="price-info">
								<view class="user-price">
									<text class="min">￥</text>
									<text class="max">{{item.vip_price}}</text>
								</view>
								<view class="vip-price">
									<!-- <image src="/static/vip_ico.png"></image> -->
									<!-- <view class="s-btn-s" @click="gotoGoodDetail(item.id)">去参拍</view> -->
									<view class="s-btn-s">去参拍</view>
								</view>
							</view>
						</view>
						<view class="on-anything">
							———— 没有更多了 ————
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<ClassifyData v-show="classifyShow!=0"></ClassifyData>
		<!-- tabbar -->
		<TabBar :tabBarShow="0"></TabBar>
	</view>
</template>

<script>
	import TabBar from '../../components/TabBar/TabBar.vue';
	// import ClassifyData from '../../components/ClassifyData/ClassifyData.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";




	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
			// ClassifyData,
		},
		data() {
			return {
				messageNum:0,
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {
					use: false
				},
				noticeList:[],
				swiperList: [{
						id: 0,
						type: 'image',
						url: '/static/img/banner_01.png',
						link: 'https://www.taobao.com' // 外部链接示例
					},
					{
						id: 1,
						type: 'image',
						url: '/static/img/banner_02.png',
						link: '/pages/baike/v?id=5' // 内部页面跳转示例
					},
					{
						id: 2,
						type: 'image',
						url: '/static/img/banner_03.png',
						link: '/pages/goods/goods' // tabBar页面跳转示例
					},
					{
						id: 3,
						type: 'image',
						url: '/static/img/banner_04.png',
						link: 'https://www.jd.com' // 另一个外部链接示例
					},
					{
						id: 4,
						type: 'image',
						url: '/static/img/banner_01.png',
						link: '/pages/search/search' // 内部页面跳转示例
					},
					{
						id: 5,
						type: 'image',
						url: '/static/img/banner_01.png',
						link: '' // 空链接，点击无效果
					}
				],
				slideNum: 0,
				navList: [{
					id: 1,
					name: '全部订单',
					url: '/pages/MyOrderList/MyOrderList?type=0'
				}, {
					id: 2,
					name: '待 付 款',
					url: '/pages/MyOrderList/MyOrderList?type=1'
				}, {
					id: 3,
					name: '待 发 货',
					url: '/pages/MyOrderList/MyOrderList?type=2'
				}, {
					id: 4,
					name: '待 收 货',
					url: '/pages/MyOrderList/MyOrderList?type=3'
				}, {
					id: 5,
					name: '已 完 成',
					url: '/pages/MyOrderList/MyOrderList?type=4'
				}, {
					id: 6,
					name: '我的团队',
					url: '/pages/Team/Team?number=5&cint=1'
				}, {
					id: 7,
					name: '我的钱包',
					url: '/pages/ConsumeRecord/ConsumeRecord'
				}, {
					id: 8,
					name: '推 广 码',
					url: '/pages/MembersOpened/MembersOpened'
				}, {
					id: 9,
					name: '赚钱模式',
					url: '/pages/ArticleDetails/ArticleDetails?id=7'
				}, {
					id: 10,
					name: '个人中心',
					url: '/pages/my/my'
				}],
				page: 1,
				classList: [{
					id: 0,
					name: '首页',
				}, {
					id: 1,
					name: '手机',
				}, {
					id: 2,
					name: '男装',
				}, {
					id: 3,
					name: '背包',
				}, {
					id: 4,
					name: '电脑',
				}, {
					id: 5,
					name: '珠宝',
				}, {
					id: 6,
					name: '美妆',
				}],
				goodsList: [],
				classifyShow: 0,
				// 页面高度
				pageHeight: 500,
				pagesize: 10,
				status: '',
				bonusList: {},
				zhiboImg: getApp().globalData.apiUrl + "/static/zhibo.png"
			}
		},
		onReady() {
			uni.hideTabBar();
			// #ifdef MP
			uni.setNavigationBarTitle({
				title: '首页',
			})
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: '#fe3b0f',
			})
			// #endif
		},
		onLoad(option) {
			var that = this
			if (option.token != undefined && option.token != "undefined") {
				getApp().globalData.token = option.token
				uni.setStorageSync("token", option.token)
			}

			this.$login.checkLogin({
				id: option.id,
				tuijiantype: option.tuijianType
			})
			this.getNotice()
			setTimeout(() => {
				getApp().appUp()
			}, 6000)
		},
		onShow() {
			this.page=1
			this.goodsList=[]
			this.getGoods()
			this.getConfig()
		},
		onPageScroll(e) {
			let scrollTop = e.scrollTop;
			if (scrollTop > 0) {
				this.pageHeight = 210;
			} else {
				this.pageHeight = 500;
			}
		},
		onReachBottom() {
			this.page++
			this.getGoods()
		},
		methods: {
			/**
			 * 通用跳转方法，兼容外部链接和内部页面跳转
			 * @param {String} link 跳转链接，可以是外部网址或内部页面路径
			 */
			handleNavigation(link) {
				// 如果没有链接，直接返回
				if (!link) {
					return;
				}

				// 判断是否为外部链接（http或https开头）
				const isExternalLink = /^https?:\/\//.test(link);

				if (isExternalLink) {
					// 处理外部链接
					this.handleExternalLink(link);
				} else {
					// 处理内部页面跳转
					this.handleInternalNavigation(link);
				}
			},

			/**
			 * 处理外部链接跳转
			 * @param {String} url 外部链接地址
			 */
			handleExternalLink(url) {
				// #ifdef APP-PLUS
				// APP环境：使用plus.runtime.openURL打开外部链接
				plus.runtime.openURL(url);
				// #endif

				// #ifdef H5
				// H5环境：使用window.open打开外部链接
				window.open(url, '_blank');
				// #endif

				// #ifdef MP
				// 小程序环境：复制链接到剪贴板并提示用户
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showModal({
							title: '提示',
							content: '链接已复制到剪贴板，请在浏览器中打开',
							showCancel: false
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
				// #endif
			},

			/**
			 * 处理内部页面跳转
			 * @param {String} path 内部页面路径
			 */
			handleInternalNavigation(path) {
				// 检查是否为tabBar页面
				const tabBarPages = [
					'/pages/home/<USER>',
					'/pages/goods/goods',
					'/pages/pai/pai',
					'/pages/my/my'
				];

				// 提取路径（去除参数）
				const pagePath = path.split('?')[0];

				if (tabBarPages.includes(pagePath)) {
					// 如果是tabBar页面，使用switchTab
					uni.switchTab({
						url: path,
						fail: (err) => {
							console.error('页面跳转失败:', err);
						}
					});
				} else {
					// 普通页面使用navigateTo
					uni.navigateTo({
						url: path,
						fail: (err) => {
							console.error('页面跳转失败:', err);
						}
					});
				}
			},

			gotoMessage(){
				uni.navigateTo({
					url:"/pages/Message/Message"
				})
			},
			onWallet($str=''){
				if($str == 'tihuo'){
					uni.navigateTo({
						url:"/pages/MyAuctionList/MyAuctionList"
					})
				}
				if($str == 'tihuoLog'){
					uni.navigateTo({
						url:"/pages/MyOrderList/MyOrderList?type=2"
					})
				}
			},
			gotoGoodDetail(cint){
				uni.navigateTo({
					url:"/pages/GoodsDetails/GoodsDetails?id="+cint
				})
			},
			gotoLink(url) {
				if (url == '/pages/my/my') {
					uni.switchTab({
						url: url
					})
				} else {
					uni.navigateTo({
						url: url
					})
				}

			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				// 检查mescroll实例是否存在，避免null错误
				if (this.mescroll && this.mescroll.endSuccess) {
					this.mescroll.endSuccess();
				} else {
					// 如果mescroll未初始化，尝试通过ref获取
					this.mescrollInitByRef();
					if (this.mescroll && this.mescroll.endSuccess) {
						this.mescroll.endSuccess();
					}
				}
			},
			getGoods() {
				uni.showLoading({
					title: "加载中..."
				})
				var that = this
				this.$http.get('getGoods', {
					goods_category_id: 1,
					page: that.page,
					limit: 10,
					token: getApp().globalData.token
				}).then(res => {
					uni.hideLoading()
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						if (res.data.length == 0) {
							this.status = 'noMore'
						} else {
							this.goodsList = this.goodsList.concat(res.data);
							if (res.data.length < this.pagesize) {
								this.status = 'noMore'
							}
						}
					} else {
						this.status = 'noMore'
					}
				})
			},
			getConfig() {
				this.$http.get('getConfig', {
					token: getApp().globalData.token
				}).then(res => {
					if (res.code == 0) {
						getApp().globalData.config = res.data
						this.swiperList = res.data.banner
						this.messageNum = res.data.messageNum[2]
					} else {
						uni.showToast({
							title: "读取配置文件错误"
						})
					}
				})
			},
			getBonus() {
				var that = this
				this.$http.get('getBonus', {
					token: getApp().globalData.token
				}).then(res => {
					if (res.code == 0) {
						that.bonusList = res.data
					}
				})
			},
			getNotice() {
				var that = this
				this.$http.get('getNoticeList2', {
					token: getApp().globalData.token
				}).then(res => {
					if (res.code == 0) {
						that.noticeList = res.data
					}
				})
			},
			/*上拉加载的回调*/
			upCallback(page) {
				setTimeout(() => {
					// 检查mescroll实例是否存在，避免null错误
					if (this.mescroll && this.mescroll.endByPage) {
						this.mescroll.endByPage(10, 20);
					} else {
						// 如果mescroll未初始化，尝试通过ref获取
						this.mescrollInitByRef();
						if (this.mescroll && this.mescroll.endByPage) {
							this.mescroll.endByPage(10, 20);
						}
					}
				}, 2000)
			},
			/**
			 * 菜单导航滚动
			 */
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			/**
			 * 搜索点击
			 */
			onSearch() {
				uni.navigateTo({
					url: '/pages/search/search'
				})
			},
			/**
			 * 扫一扫点击
			 */
			onCode() {
				// 只允许通过相机扫码
				uni.scanCode({
					onlyFromCamera: true,
					success: function(res) {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
					}
				});
			},
			/**
			 * 分类点击
			 * @param {Object} item
			 * @param {Number} index
			 */
			onClassify(item, index) {
				this.classifyShow = index;
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type, id) {
				switch (type) {
					case 'mess':
						uni.navigateTo({
							url: '/pages/Message/Message'
						})
						break;
					case 'notice':
						uni.navigateTo({
							url: '/pages/ArticleDetails/ArticleDetails?id='+id
						})
						break;
					case 'paycode':
						uni.navigateTo({
							url: '/pages/PaymentCode/PaymentCode'
						})
						break;
					case 'myCode':
						uni.navigateTo({
							url: '/pages/MembersOpened/MembersOpened'
						})
						break;
					case 'menu':
						uni.navigateTo({
							url: '/pages/SearchGoodsList/SearchGoodsList'
						})
						break;
					case 'inform':
						break;
					case 'flash':
						uni.navigateTo({
							url: '/pages/FlashSale/FlashSale'
						})
						break;
					case 'GoodChoice':
						uni.navigateTo({
							url: '/pages/GoodChoice/GoodChoice'
						})
						break;
					case 'goods':
						uni.switchTab({
							// url: '/pages/GoodsDetails/GoodsDetails?id=' + id,
							url: '/pages/pai/pai',
							animationType: 'zoom-fade-out',
							animationDuration: 200
						})
						break;
				}
			}
		}
	};
</script>

<style scoped lang="scss">
	@import 'home.scss';
</style>
