module.exports ={
    checkLogin:function(res){
		//调试开始
		// uni.setStorageSync('token','7a0603676a4a4c18c1dd04fbd8bfc08c')  // 注意app。js中的apiURL 
		// uni.setStorageSync('token','47e908bc658e2aa9e3b96b6d35960e62')  // 其他tokenb
		//调试结束
		// uni.setStorageSync('token',null)  // 注意app。js中的apiURL
		var url = getApp().globalData.apiUrl+"/api/wxlogin"
		if(typeof(res)!="undefined" && res != null){
			url += "?id="+res.id+"&tuijianType="+res.tuijianTypess
		}
		getApp().globalData.token = uni.getStorageSync("token")
		// console.log(getApp().globalData.token)
		if(getApp().globalData.token==null || getApp().globalData.token.length!=32){
			// #ifdef H5
				var ua = window.navigator.userAgent.toLowerCase();
				if (ua.match(/MicroMessenger/i) == 'micromessenger' && 1==2) {
			        window.open(url,"_self"); // 微信中打开
			    } else {
					if(typeof(res)!=null && typeof(res)!='undefined'){
						if(typeof(res.login)!=undefined && res.login==true){
							uni.showToast({
								title:"需登录"
							})
							setTimeout(function(){
								window.open("/#/pages/login/login","_self");
							},1500)
							return false;
						}
					}
			        
			    }
				// window.open(url,"_self"); // 微信中打开
			// #endif
		}
		return true;
    },
	gotoLogin:function(json){
		if(json.data.code == 100){
			uni.showToast({
				title:'登录超时'
			})
			var url = getApp().globalData.apiUrl+"/api/wxlogin"
			
			if(typeof(json)!="undefined" && json != null){
				url += "?id="+json.id+"&tuijianType="+json.tuijianType
			}
			
			setTimeout(function(){
				// #ifdef H5
					window.open(url,"_self")
				// #endif
			},1500)
			
		}
	},
	get7days:function(cint){
		var days = 7; // 近7天
		if(cint>7){
			var days = cint;
		}
		const week = ['日', '一', '二', '三', '四', '五', '六']; // 星期
		const dateList = Array.from({ length: days }, (v, i) => i).map(day => {
		  const date = new Date();
		  date.setDate(date.getDate() + day);
		  
		  // return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' 周' + week[date.getDay()];
		  var weekStr=''
		  if(day==0){
			  //今天
			  weekStr = ' 今天'
		  }else if(day==1){
			  //今天
			  weekStr = ' 明天'
		  }else{
			  weekStr = ' 周' + week[date.getDay()]
		  }
		  return ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0'+date.getDate()).slice(-2) + weekStr;
		});
		const fullDate = Array.from({ length: days }, (v, i) => i).map(day => {
		  const date = new Date();
		  date.setDate(date.getDate() + day);
		  
		  // return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' 周' + week[date.getDay()];
		  return date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0'+date.getDate()).slice(-2);
		});
		// console.log(dateList);
		return [dateList,fullDate]
	},
	getCookie:function(name){
		var arrStr = document.cookie.split("; ");
		for (var i = 0; i < arrStr.length; i++) {
			var temp = arrStr[i].split("=");
			if (temp[0] == name){
				return temp[1]
			}
		} 
	}
}