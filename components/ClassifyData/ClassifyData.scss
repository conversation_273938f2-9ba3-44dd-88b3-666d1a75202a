.page-total {
	position: absolute;
	left: 0;
	top: 200rpx;
	/* #ifdef APP-PLUS */
	top: calc(180rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef MP */
	top: calc(100rpx + var(--status-bar-height));
	/* #endif */
	padding-bottom: 20rpx;
	width: 100%;
	// height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	background-color: #f2f2f2;
	padding-bottom: 120rpx;
}
// 分类列表
.classify-list {
	display: flex;
	flex-wrap: wrap;
	padding: 10rpx 25rpx;
	background-color: #ffffff;
	border-radius: 0 0 20rpx 20rpx;
	margin: 20rpx auto;
	.list {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 20%;
		height: 150rpx;
		.thumb {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 70%;
			image {
				width: 80rpx;
				height: 80rpx;
			}
		}
		.name {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 30%;
			text {
				color: #212121;
				font-size: 26rpx;
			}
		}
	}
}
// 超值爆款
.super-hot-style {
	width: 100%;
	background-color: #ffffff;
	border-radius: 20rpx;
	.hot-title {
		display: flex;
		align-items: center;
		padding: 0 4%;
		height: 80rpx;
		.iconfont {
			font-size: 38rpx;
			color: $base;
		}
		.title {
			font-size: 30rpx;
			color: #212121;
			margin-left: 10rpx;
		}
	}
	.goods-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0 4%;
		padding-bottom: 20rpx;
		.list {
			width: 32%;
			margin-right: 2%;
			.thumb {
				width: 100%;
				image {
					width: 100%;
				}
			}
			.title {
				display: flex;
				width: 100%;
				padding: 10rpx 0;
				text {
					color: #212121;
					font-size: 26rpx;
				}
			}
			.price {
				padding: 10rpx 0;
				.sales-volume {
					font-size: 24rpx;
					color: #c0c0c0;
					margin-top: 5rpx;
				}
				.retail-price {
					width: 100%;
					display: flex;
					align-items: center;
					.min {
						display: inline-block;
						font-size: 24rpx;
						color: $price-clor;
						transform: scale(0.8);
					}
					.max {
						font-size: 26rpx;
						color: $price-clor;
						font-weight: bold;
					}
				}
				.vip-price {
					display: flex;
					align-items: center;
					.min {
						display: inline-block;
						font-size: 24rpx;
						color: #212121;
					}
					.max {
						font-size: 24rpx;
						color: #212121;
						font-weight: bold;
						margin-right: 10rpx;
					}
				}
			}
		}
		.list:nth-child(3n) {
			margin-right: 0;
		}
	}
}
// 更多热卖
.more-hot {
	width: 100%;
	background-color: #f2f2f2;
	.hot-title {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		.title {
			font-size: 32rpx;
			color: $base;
			.icon {
				margin: 0 20rpx;
			}
		}
	}
  .goods-list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 30rpx;
    .list{
      width: 49%;
      height: 540rpx;
      margin-bottom: 20rpx;
      background-color: #FFFFFF;
      border-radius: 10rpx;
      overflow: hidden;
      .pictrue{
        display: flex;
        justify-content: center;
        width: 100%;
        image{
          height: 350rpx;
        }
      }
      .title-tag{
        // display: flex;
        height: 100rpx;
        padding: 20rpx;
        .tag{
          float: left;
          margin-right: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;
          font-size: 26rpx;
          line-height: 40rpx;
          text{
            font-size: 24rpx;
            color: #FFFFFF;
            padding: 4rpx 16rpx;
            background: linear-gradient(to right,$base,$change-clor);
            border-radius: 6rpx;
            margin-right: 10rpx;
          }
        }
      }
      .price-info{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        height: 80rpx;
        .user-price{
          display: flex;
          align-items: center;
          text{
            color: $price-clor;
          }
          .min{
            font-size: 24rpx;
          }
          .max{
            font-size: 32rpx;
          }
        }
        .vip-price{
          display: flex;
          align-items: center;
          image{
            width: 26rpx;
            height: 26rpx;
            margin-right: 10rpx;
          }
          text{
            color: #fcb735;
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
