<template>
	<view class="flex loginContract flexItemCenter">
		<checkbox :color="color"  @click="agree" value="1"/>我已阅读并同意
		<block v-for="(item,index) in listArr" :key="">
			<block v-if="index>0">和</block>
			<view class="green" @tap="r_artice(item.id)">《{{item.title}}》</view>
		</block>
	</view>	
</template>

<script>
	export default {
		name:"xieyi",
		props:{
			listArr:{}
		},
		data() {
			return {
				color:getApp().globalData.color
			};
		},
		methods:{
			r_artice(cint){
				this.$openInvite({id:cint,table:'artice'})
			},
			agree(e){
				this.$emit('agree',e);
			}
		}
	}
</script>

<style lang="scss">
</style>