.page-total{
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background-color: #f6f6f6;
}

.key-list{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	padding: 0 3%;
	height: 90%;
	margin-top: 20rpx;
	.list{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 32.3%;
		height: 80rpx;
		background-color: #FFFFFF;
		border-radius: 10rpx;
		box-shadow: 0 0 10rpx rgba(0,0,0,0.1);
		margin-right: 1.5%;
		margin-bottom: 10rpx;
		text{
			font-size: 38rpx;
			font-weight: bold;
			color: #222222;
		}
	}
	.list:nth-child(3n){
		margin-right: 0;
	}
	.special{
		background-color: #f6f6f6;
		box-shadow: none;
		text{
			color: #959595;
		}
	}
}