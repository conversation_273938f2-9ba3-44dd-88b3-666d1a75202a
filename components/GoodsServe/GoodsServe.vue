<template>
	<view class="page-total" v-show="isPage" @click="hide">
		<view class="dialog" :class="{'show':isShow}">
			<view class="serve-list">
				<view class="list">
					<view class="title">
						<text>名称</text>
					</view>
					<view class="content">
						<text>{{auction.title}}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>联系人</text>
					</view>
					<view class="content">
						<text>{{auction.contacts}}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>电话</text>
					</view>
					<view class="content">
						<text>{{auction.mobile}}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>资质证件</text>
					</view>
					<view class="content" style="display: flex;flex-direction: column;">
						<block v-for="(item,index) in auction.files" :key="index">
							<image :src="item" style="width: 100%;margin-bottom: 50rpx;" mode="aspectFit"></image>
						</block>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isShow: false,
				api:getApp().globalData.apiUrl,
				isPage: false,
				auction:{
					auction:'',
					contacts:'',
					mobile:'',
					files:[],
				},
			};
		},
		props:{
			id:0
		},
		mounted() {
			// this.getAuction()
		},
		methods:{
			getAuction(cint){
				this.id = cint
				var that = this
				this.$http.get('getAuction', {
					id: that.id
				}).then(res => {
					if (res.code == 0) {
						this.auction = res.data
						this.isPage = true;
						setTimeout(()=>{
							this.isShow = true;
						},200)
					} else {
						uni.showToast({
							title: res.msg
						})
						this.status = 'noMore'
					}
				})
			},
			show(cint){
				console.log(cint)
				this.getAuction(cint)
				
			},
			hide(){
				this.isShow = false;
				setTimeout(()=>{
					this.isPage = false;
				},200)
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'GoodsServe.scss';
</style>
