<template>
	<view class="cu-modal bottom-modal" :class="{'show':isShow}" @click="hide">
	  <view class="cu-dialog">
			<view class="goods-data">
				<view class="thumb">
					<image src="https://axhub.im/pro/bb1f7bd347952764/images/%E9%A6%96%E9%A1%B5/u640-0.png" mode=""></image>
				</view>
				<view class="item">
					<view class="title">
						<text></text>
					</view>
					<view class="price">
						<text class="min">￥</text>
						<text class="max">{{goodsArr.price1}}</text>
						<!-- <text class="min">.00</text> -->
					</view>
					<view class="inventory">
						<text>数量：{{userinfo.canBuy}}</text>
					</view>
				</view>
			</view>
			<view class="attr-size">

				<view class="attr-number" @click.stop="onStop">
					<view class="tit">数量</view>
					<view class="number">
						<text class="iconfont icon-jian" @click="jiajia(-1)"></text>
						<input type="tel" v-model="number" maxlength="8">
						<text class="iconfont icon-jia" @click="jiajia(1)"></text>
					</view>
				</view>

				<view class="attr-number" @click.stop="onStop">
					<view class="tit">快捷输入</view>
					<view class="number">
						<text @click="jia(0)" style="border: 1px solid #ccc;margin-left: 20rpx;padding: 4rpx 16rpx;font-size: 110%;">重置</text>
						<text @click="jia(userinfo.canBuy)" style="border: 1px solid #ccc;margin-left: 20rpx;padding: 4rpx 16rpx;font-size: 110%;color: red;">全部</text>
					</view>
				</view>
			</view>
			<view class="attr-btn">
				<view class="add-cart" v-if="BuyType === 1" @click="onConfirm(BuyType)">加入购物车</view>
				<view class="add-buy" v-if="BuyType === 1" @click="onConfirm(BuyType)">立即购买</view>
				<view class="confirm" v-if="BuyType === 2 || BuyType === 3 || BuyType ===4" @click="onConfirm(BuyType)">确定</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			goodsArr:{},
			id:0,
			token:''
		},
		data() {
			return {
				number:1,
				numbers:0,
				isShow: false,
				AttrIndex: 0,
				SizeIndex: 0,
				userinfo:[],
				AttrSizeList:[
					{
						index: 0,
						attr: '颜色',
						SizeList: [
							{
								index: 0,
								size: '白色'
							},{
								index: 1,
								size: '黑色'
							},{
								index: 2,
								size: '粉丝'
							},{
								index: 3,
								size: '灰色'
							},
						],
					},{
						index: 0,
						attr: '尺码',
						SizeList: [
							{
								index: 0,
								size: 'M尺码'
							},{
								index: 1,
								size: 'L尺码'
							},{
								index: 2,
								size: 'XL尺码'
							},{
								index: 3,
								size: 'XXL尺码'
							},
						],
					}
				],
				// 购买类型
				BuyType: 0,
			};
		},
		methods:{
			/**
			 * 显示 
			 * @param {Number} type 1 点击选择 2 加入购物 3 立即购买 4 委拍
			 */
			show(type){
				this.BuyType = type
				this.getUserInfo(type)
				// this.isShow = true;
			},
			getUserInfo(type){
				var that = this
				this.$http.get('getUserInfo', {
					token:that.token,
					price:that.goodsArr.price1,
					type:type,
					goodsID:that.goodsArr.id,
					round:that.goodsArr.round
				}).then(res => {
					if (res.code == 0) {
						that.userinfo = res.data
						setTimeout(()=>{
							this.isShow = true;
						},200)
					} else {
						uni.showToast({
							title: res.msg
						})
					}
				})
			},
			jia($cint){
				this.number = $cint
				if(this.number<1){
					this.number=1
				}
			},
			jiajia($cint){
				this.number = this.number*1 + $cint*1
				if(this.number<1){
					this.number=1
				}
			},
			hide(){
				this.isShow = false;
			},
			onStop(){
				
			},
			/**
			 * 属性选择点击
			 */
			onAttrSize(item,value,index,idx){
				this.AttrSizeList[index].index = idx;
				this.AttrIndex = item.index;
				this.SizeIndex = value.index;
			},
			/**
			 * 确认点击
			 */
			onConfirm(type){
				// uni.navigateTo({
				// 	url: '/pages/ConfirmOrder/ConfirmOrder?id='+this.goodsArr.id+"&number="+this.number
				// })
				var that = this
				this.$http.get('postAuction', {
					token:that.token,
					number:that.number,
					goodsID:that.goodsArr.id,
					fontRound:that.goodsArr.fontRound,
					type:type
				}).then(res => {
					that.hide()
					uni.showToast({
						title: res.msg
					})
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'GoodsAttr.scss';
</style>
