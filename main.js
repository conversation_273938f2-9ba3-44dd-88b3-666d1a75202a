import Vue from 'vue';
import App from './App';
import http from "@/common/http.js"
import login from "@/common/login.js"
// 弹出框
import DialogBox from './components/DialogBox/DialogBox';


Vue.config.productionTip = false
Vue.prototype.$http = http;
Vue.prototype.$login = login;

// 全局组件
Vue.component('DialogBox', DialogBox);
//mescroll
import MescrollBody from "@/components/mescroll-uni/mescroll-body.vue"
import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue"
Vue.component('mescroll-body', MescrollBody)
Vue.component('mescroll-uni', MescrollUni)

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()